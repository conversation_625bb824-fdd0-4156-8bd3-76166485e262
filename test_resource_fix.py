#!/usr/bin/env python3
"""
测试资源修复效果的脚本
验证Chrome进程控制和资源管理是否正常工作
"""

import asyncio
import time
import psutil
import logging
from playwright.async_api import async_playwright

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def count_chrome_processes():
    """统计Chrome进程数量"""
    chrome_count = 0
    for proc in psutil.process_iter(['pid', 'name']):
        try:
            if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                chrome_count += 1
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    return chrome_count

def get_memory_usage():
    """获取内存使用情况"""
    memory = psutil.virtual_memory()
    return memory.percent

async def test_browser_launch():
    """测试浏览器启动和资源控制"""
    logger.info("开始测试浏览器启动...")
    
    # 记录初始状态
    initial_chrome_count = count_chrome_processes()
    initial_memory = get_memory_usage()
    logger.info(f"初始状态 - Chrome进程: {initial_chrome_count}, 内存: {initial_memory:.1f}%")
    
    # 严格的资源控制启动参数
    launch_args = [
        '--no-sandbox',
        '--disable-dev-shm-usage',
        '--disable-setuid-sandbox',
        '--no-zygote',
        '--disable-gpu',
        '--disable-gpu-sandbox',
        '--disable-software-rasterizer',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-features=TranslateUI,VizDisplayCompositor',
        '--disable-ipc-flooding-protection',
        '--disable-background-networking',
        '--disable-sync',
        '--disable-default-apps',
        '--no-first-run',
        '--disable-extensions',
        '--disable-plugins',
        '--disable-component-extensions-with-background-pages',
        '--force-color-profile=srgb',
        '--memory-pressure-off',
        '--max_old_space_size=1024',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--disable-blink-features=AutomationControlled',
        '--disable-video-capture',
        '--disable-camera',
        '--disable-microphone',
        '--disable-accelerated-video-decode',
        '--disable-accelerated-video-encode',
        '--disable-notifications',
        '--disable-web-bluetooth',
        '--disable-usb-keyboard-detect',
        '--max-decoded-image-size-mb=32',
        '--renderer-process-limit=1',
        '--max-unused-resource-memory-usage-percentage=10',
        '--thread-pool-size=2',
        '--max-concurrent-runs=1',
        '--num-raster-threads=1',
        '--force-device-scale-factor=1',
        '--disable-background-media-suspend',
        '--disable-audio-output',
        '--disable-media-stream',
        '--disable-webgl',
        '--disable-webgl2',
        '--disable-databases',
        '--disable-local-storage',
        '--disable-session-storage',
        '--disable-file-system',
        '--single-process',  # 强制单进程模式
    ]
    
    playwright = None
    browser = None
    
    try:
        # 启动Playwright
        playwright = await async_playwright().start()
        logger.info("Playwright启动成功")
        
        # 启动浏览器
        browser = await playwright.chromium.launch(
            headless=True,
            args=launch_args
        )
        logger.info("浏览器启动成功")
        
        # 检查进程数量
        after_launch_chrome_count = count_chrome_processes()
        after_launch_memory = get_memory_usage()
        logger.info(f"启动后状态 - Chrome进程: {after_launch_chrome_count}, 内存: {after_launch_memory:.1f}%")
        
        # 创建页面并访问网站
        context = await browser.new_context()
        page = await context.new_page()
        
        logger.info("访问测试网站...")
        await page.goto("https://www.baidu.com", timeout=30000)
        logger.info("网站访问成功")
        
        # 等待一段时间
        await asyncio.sleep(5)
        
        # 再次检查资源
        final_chrome_count = count_chrome_processes()
        final_memory = get_memory_usage()
        logger.info(f"最终状态 - Chrome进程: {final_chrome_count}, 内存: {final_memory:.1f}%")
        
        # 关闭页面和上下文
        await page.close()
        await context.close()
        
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
        
    finally:
        # 清理资源
        if browser:
            try:
                await browser.close()
                logger.info("浏览器已关闭")
            except Exception as e:
                logger.error(f"关闭浏览器时出错: {e}")
        
        if playwright:
            try:
                await playwright.stop()
                logger.info("Playwright已停止")
            except Exception as e:
                logger.error(f"停止Playwright时出错: {e}")
        
        # 等待进程完全清理
        await asyncio.sleep(3)
        
        # 最终检查
        cleanup_chrome_count = count_chrome_processes()
        cleanup_memory = get_memory_usage()
        logger.info(f"清理后状态 - Chrome进程: {cleanup_chrome_count}, 内存: {cleanup_memory:.1f}%")
        
        # 验证结果
        if cleanup_chrome_count <= 2:
            logger.info("✅ Chrome进程控制正常")
        else:
            logger.warning(f"⚠️ Chrome进程数量仍然较多: {cleanup_chrome_count}")
        
        if cleanup_memory < 80:
            logger.info("✅ 内存使用正常")
        else:
            logger.warning(f"⚠️ 内存使用率较高: {cleanup_memory:.1f}%")

async def main():
    """主函数"""
    logger.info("开始资源修复效果测试...")
    
    # 运行测试
    await test_browser_launch()
    
    logger.info("测试完成！")

if __name__ == "__main__":
    asyncio.run(main())
