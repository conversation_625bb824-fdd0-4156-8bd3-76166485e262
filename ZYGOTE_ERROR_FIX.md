# Zygote 进程启动失败问题解决方案

## 问题描述

在长时间运行（1-2天后）的Playwright自动化任务中，经常出现以下错误：

```
FATAL:zygote_host_impl_linux.cc(184)] Check failed: process.IsValid(). Failed to launch zygote process
BrowserType.launch: Target page, context or browser has been closed
```

这个问题主要出现在Linux系统上，是由于长时间运行导致的系统资源累积和进程管理问题。

## 解决方案

### 1. 浏览器启动参数优化

已在 `task_runner.py` 中增加了以下关键启动参数：

```python
launch_args = [
    '--no-sandbox',
    '--disable-dev-shm-usage',
    '--disable-setuid-sandbox',
    '--no-zygote',  # 禁用zygote进程，避免zygote相关错误
    '--single-process',  # 使用单进程模式，减少进程管理复杂度
    '--disable-gpu',
    '--disable-gpu-sandbox', 
    '--memory-pressure-off',  # 关闭内存压力检测
    '--max_old_space_size=4096'  # 增加V8堆内存限制
]
```

### 2. 系统资源清理

#### 自动清理守护进程
系统会自动启动清理守护进程，每30分钟执行一次：
- 清理临时目录中的Playwright文件
- 终止僵尸Chromium进程
- 监控系统资源使用情况

#### 配置选项（config.yaml）
```yaml
SYSTEM_CLEANUP_INTERVAL_MINUTES: 30  # 清理间隔
MAX_TEMP_FILES_AGE_HOURS: 6          # 临时文件保留时间
ENABLE_SYSTEM_CLEANUP: true          # 启用自动清理
```

### 3. 智能错误处理和重试

当检测到zygote错误时，系统会：
1. 立即执行强制清理
2. 等待3秒让系统恢复
3. 自动重试启动浏览器（仅一次）

### 4. 自动监控重启脚本

提供了 `restart_on_error.py` 脚本，用于监控和自动重启：

```bash
# 使用监控重启脚本
python restart_on_error.py
```

#### 监控功能：
- 实时监控主程序运行状态
- 检测关键错误并自动重启
- 限制重启频率（每小时最多10次）
- 重启前自动清理系统资源

## 部署建议

### 1. 生产环境推荐配置

```yaml
# config.yaml 中的关键配置
HEADLESS_BROWSER: true              # 生产环境使用无头模式
MAX_TASK_RUN_DURATION_SECONDS: 180  # 降低单任务超时时间
SYSTEM_CLEANUP_INTERVAL_MINUTES: 20 # 更频繁的清理
ENABLE_SYSTEM_CLEANUP: true         # 必须启用
```

### 2. 使用监控重启脚本

生产环境推荐使用监控脚本：

```bash
# 后台运行监控器
nohup python restart_on_error.py > monitor.log 2>&1 &

# 或使用systemd服务（推荐）
sudo systemctl start chatbot-automation-monitor
```

### 3. 系统级优化

对于Linux服务器，建议：

```bash
# 增加文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 增加共享内存大小
echo "tmpfs /dev/shm tmpfs defaults,size=2g 0 0" >> /etc/fstab

# 定期清理临时文件（crontab）
0 */6 * * * find /tmp -name "*playwright*" -mtime +1 -exec rm -rf {} \; 2>/dev/null
```

## 故障排查

### 手动清理命令

如果问题持续存在，可以手动执行清理：

```bash
# 终止所有Chromium进程
pkill -f chromium

# 清理临时文件
find /tmp -name "*playwright*" -exec rm -rf {} \; 2>/dev/null
find /tmp -name "*chromium*" -exec rm -rf {} \; 2>/dev/null

# 检查系统资源
free -h
ps aux | grep chromium | wc -l
```

### 日志监控

关键错误日志关键词：
- `Failed to launch zygote process`
- `Check failed: process.IsValid()`
- `FATAL:zygote_host_impl_linux.cc`
- `Browser has been closed`

### 监控指标

建议监控：
- 内存使用率
- 临时文件数量
- Chromium进程数量
- 程序重启频率

## 预防措施

1. **定期重启**: 建议每24小时重启一次程序
2. **资源监控**: 监控内存和临时文件使用情况
3. **日志轮转**: 避免日志文件过大
4. **系统维护**: 定期清理系统临时文件

## 测试验证

部署后可以通过以下方式验证：

```bash
# 检查自动清理是否工作
tail -f chatbot_automation.log | grep "系统清理"

# 监控进程数量
watch "ps aux | grep chromium | wc -l"

# 检查临时文件
watch "find /tmp -name '*playwright*' | wc -l"
```

## 应急处理

如果问题仍然频繁出现：

1. 增加清理频率到10分钟
2. 降低并发任务数
3. 增加任务间隔时间
4. 考虑使用容器化部署
5. 联系技术支持进行深度分析 