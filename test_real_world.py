#!/usr/bin/env python3
"""
真实环境功能测试 - 在实际目标网站测试功能
"""

import asyncio
import sys
from task_runner import TaskRunner
from utils.logger import logger
import yaml

async def test_real_functionality():
    """在真实网站环境下测试功能"""
    logger.info("开始真实环境功能测试...")
    
    # 加载配置
    try:
        with open('config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        logger.info("配置加载成功")
    except Exception as e:
        logger.error(f"配置加载失败: {e}")
        return False
    
    # 创建测试任务
    task_runner = TaskRunner(config=config, task_id=999, should_perform_chat_interaction=True)
    
    try:
        # 初始化浏览器
        await task_runner._initialize_browser()
        logger.info("✅ 浏览器初始化成功")
        
        # 测试获取目标URL
        target_url = await task_runner._get_target_url()
        if target_url:
            logger.info(f"✅ 目标URL获取成功: {target_url}")
        else:
            logger.warning("⚠️ 目标URL获取失败")
            return False
        
        # 测试访问目标网站
        await task_runner.page.goto(target_url, wait_until='networkidle', timeout=30000)
        logger.info("✅ 目标网站访问成功")
        
        # 测试页面功能
        title = await task_runner.page.title()
        logger.info(f"页面标题: {title}")
        
        # 测试JavaScript执行
        try:
            result = await task_runner.page.evaluate('typeof window !== "undefined"')
            if result:
                logger.info("✅ JavaScript环境正常")
            else:
                logger.warning("⚠️ JavaScript环境异常")
        except Exception as e:
            logger.warning(f"⚠️ JavaScript测试失败: {e}")
        
        # 测试查找客服按钮
        customer_service_selector = config.get('CUSTOMER_SERVICE_BUTTON_SELECTOR', '#ai-chat-button')
        try:
            button = await task_runner.page.query_selector(customer_service_selector)
            if button:
                logger.info("✅ 客服按钮找到")
                
                # 测试点击（但不实际执行聊天）
                await button.click()
                await asyncio.sleep(2)
                
                # 检查iframe是否出现
                iframe_selector = config.get('IFRAME_SELECTOR', '#edu-bot-iframe2')
                iframe = await task_runner.page.query_selector(iframe_selector)
                if iframe:
                    logger.info("✅ 聊天iframe正常加载")
                else:
                    logger.warning("⚠️ 聊天iframe未找到")
                    
            else:
                logger.warning("⚠️ 客服按钮未找到")
        except Exception as e:
            logger.warning(f"⚠️ 客服按钮交互测试失败: {e}")
        
        # 测试浏览器指纹
        fingerprint = await task_runner.page.evaluate("""
            () => {
                return {
                    userAgent: navigator.userAgent,
                    language: navigator.language,
                    languages: navigator.languages,
                    platform: navigator.platform,
                    cookieEnabled: navigator.cookieEnabled,
                    webdriver: navigator.webdriver,
                    plugins: navigator.plugins.length,
                    screenWidth: screen.width,
                    screenHeight: screen.height,
                    timezoneOffset: new Date().getTimezoneOffset(),
                    chrome: !!window.chrome
                };
            }
        """)
        
        logger.info("浏览器指纹信息:")
        for key, value in fingerprint.items():
            logger.info(f"  {key}: {value}")
        
        # 评估指纹真实性
        realistic_score = 0
        total_checks = 8
        
        if fingerprint['userAgent'] and 'Mozilla' in fingerprint['userAgent']:
            realistic_score += 1
        if fingerprint['language'] == 'zh-CN':
            realistic_score += 1
        if fingerprint['languages'] and len(fingerprint['languages']) > 1:
            realistic_score += 1
        if fingerprint['cookieEnabled']:
            realistic_score += 1
        if fingerprint['webdriver'] is None or fingerprint['webdriver'] == False:
            realistic_score += 1
        if fingerprint['plugins'] > 0:
            realistic_score += 1
        if fingerprint['chrome']:
            realistic_score += 1
        if fingerprint['timezoneOffset'] == -480:  # 北京时间
            realistic_score += 1
        
        realism_percentage = (realistic_score / total_checks) * 100
        logger.info(f"浏览器真实性评分: {realistic_score}/{total_checks} ({realism_percentage:.1f}%)")
        
        if realism_percentage >= 75:
            logger.info("🟢 浏览器指纹高度真实")
            return True
        elif realism_percentage >= 50:
            logger.info("🟡 浏览器指纹基本真实")
            return True
        else:
            logger.warning("🔴 浏览器指纹可能被检测")
            return False
            
    except Exception as e:
        logger.error(f"真实环境测试失败: {e}", exc_info=True)
        return False
    finally:
        # 清理资源
        try:
            await task_runner._cleanup_browser()
        except Exception as e:
            logger.error(f"清理资源失败: {e}")

async def main():
    """主函数"""
    success = await test_real_functionality()
    
    if success:
        print("\n🎉 真实环境测试通过 - 修改对用户模拟功能影响很小")
        sys.exit(0)
    else:
        print("\n⚠️ 真实环境测试发现问题 - 建议进一步调整配置")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main()) 