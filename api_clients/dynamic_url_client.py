import asyncio
import random
import requests # 按照现有模式使用 requests 和 to_thread
from typing import List, Dict, Optional

from utils.logger import logger

class DynamicURLError(Exception):
    """动态URL获取错误的自定义异常。"""
    pass

class NoProjectNamesError(DynamicURLError):
    """当项目名称列表为空时抛出。"""
    pass

def _fetch_url_sync(api_endpoint: str, params: Dict, headers: Dict) -> str:
    """同步函数，用于获取URL，预期在单独的线程中运行。"""
    try:
        logger.debug(f"正在从 {api_endpoint} 获取动态URL，参数: {params}")
        response = requests.get(api_endpoint, params=params, headers=headers, timeout=10) # 10秒超时
        response.raise_for_status() # 对错误响应(4XX, 5XX)抛出HTTPError
        
        data = response.json()
        logger.debug(f"收到API响应: {data}")

        if data.get("status") != "success" or "url" not in data or not data["url"]:
            err_msg = data.get('msg', 'API未知错误或URL为空')
            logger.error(f"API指示失败，响应中缺少'url'或状态不是success: {data}。消息: {err_msg}")
            raise DynamicURLError(f"API错误: {err_msg}")
        
        # 验证URL是否为字符串且格式正确（基本检查）
        url_value = data["url"]
        if not isinstance(url_value, str) or not (url_value.startswith('http://') or url_value.startswith('https://')) :
            logger.error(f"获取的URL不是有效的字符串或格式: {url_value}")
            raise DynamicURLError(f"获取的URL '{url_value}' 不是有效的字符串或格式。")
            
        return url_value
    except requests.exceptions.HTTPError as e:
        logger.error(f"从 {api_endpoint} 获取动态URL时发生HTTP错误: {e.response.status_code} - {e.response.text}")
        raise DynamicURLError(f"HTTP错误: {e.response.status_code}") from e
    except requests.exceptions.RequestException as e: # 捕获其他requests异常，如ConnectionError、Timeout
        logger.error(f"从 {api_endpoint} 获取动态URL时发生请求异常: {e}")
        raise DynamicURLError(f"请求失败: {e}") from e
    except ValueError as e: # JSONDecodeError继承自ValueError
        logger.error(f"从 {api_endpoint} 获取动态URL时发生JSON解码错误: {e}。响应文本: {response.text if 'response' in locals() else 'N/A'}")
        raise DynamicURLError("API返回的JSON响应无效") from e

async def get_dynamic_target_url(
    api_endpoint: str, 
    api_key: str,
    project_names: List[str]
) -> str:
    """
    异步获取动态目标URL。
    失败时抛出DynamicURLError或NoProjectNamesError。
    """
    if not api_endpoint or not api_key:
        logger.error("动态URL API端点或API密钥未正确配置。")
        raise DynamicURLError("动态URL API端点或API密钥未配置。")
    
    if not project_names:
        logger.error("动态URL的项目名称列表为空。")
        raise NoProjectNamesError("项目名称列表不能为空。")

    selected_project_name = random.choice(project_names)
    logger.info(f"已选择项目名称 '{selected_project_name}' 用于获取动态URL。")

    params = {"project_name": selected_project_name}
    headers = {"X-API-KEY": api_key}

    try:
        # 确保同步函数在单独的线程中调用
        url = await asyncio.to_thread(_fetch_url_sync, api_endpoint, params, headers)
        logger.info(f"成功获取动态URL: {url}，项目: {selected_project_name}")
        return url
    except DynamicURLError as e: # 捕获同步函数抛出的特定错误
        logger.error(f"获取项目 '{selected_project_name}' 的动态URL失败: {e}")
        raise # 重新抛出，让调用者(TaskRunner)处理
    except Exception as e: # 捕获其他意外错误
        logger.critical(f"获取项目 '{selected_project_name}' 的动态URL时发生意外错误: {e}", exc_info=True)
        raise DynamicURLError(f"获取动态URL时发生意外错误: {e}") from e 