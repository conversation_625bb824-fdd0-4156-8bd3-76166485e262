# Placeholder for api_clients/proxy_client.py 

import requests
from utils.logger import logger
from utils.decorators import retry

# 异常可以根据实际API返回情况定义得更具体
class ProxyAPIError(Exception):
    """自定义代理API错误。"""
    pass

class NoAvailableProxyError(ProxyAPIError):
    """没有可用代理的错误。"""
    pass

@retry(attempts=3, delay_seconds=5, accepted_exceptions=(requests.exceptions.RequestException, ProxyAPIError))
def get_proxy_ip(api_url: str, api_key: str, params: dict = None) -> dict:
    """
    从指定的API获取代理IP。

    :param api_url: 代理API的基础URL。
    :param api_key: 您的API密钥。
    :param params: 其他API参数，如 protocol, type 等。会与 key 合并。
    :return: 一个包含 'ip' 和 'port' 的字典。
    :raises ProxyAPIError: 如果API返回错误或数据格式不正确。
    :raises NoAvailableProxyError: 如果API调用成功但没有返回代理IP数据。
    :raises requests.exceptions.RequestException: 如果请求过程中发生网络错误。
    """
    default_params = {
        "key": api_key,
        "count": 1,        # 默认获取1个
        "protocol": 0,     # 默认为HTTP/HTTPS
        "type": 0,         # 默认透明代理
        "distinct": True,  # 确保获取的IP不重复（如果API支持）
        "textSep": "1",    # 根据您提供的API，这可能是指JSON格式或特定分隔符
                            # 若是JSON，通常不需要，这里保留以提醒
        # "is": 0, # 这些参数在您的示例URL中，但未在响应中体现，可以根据需要添加
        # "es": 0,
        # "os": 1,
        # "cs": 1,
    }
    if params:
        default_params.update(params)

    logger.debug(f"正在从 {api_url} 请求代理IP，参数: {default_params}")
    
    try:
        response = requests.get(api_url, params=default_params, timeout=10) # 设置超时
        response.raise_for_status()  # 如果HTTP状态码是4xx或5xx，则抛出HTTPError
    except requests.exceptions.RequestException as e:
        logger.error(f"请求代理API时出错: {e}")
        raise

    try:
        data = response.json()
    except requests.exceptions.JSONDecodeError as e:
        logger.error(f"解析代理API的JSON响应失败: {response.text[:500]}...") # 限制日志长度
        raise ProxyAPIError(f"JSON解码错误: {e}。响应体(前500字符): {response.text[:500]}") from e

    logger.debug(f"代理API原始响应: {data}")

    if str(data.get("code")) != "0":
        error_msg = data.get("msg", "来自代理API的未知错误")
        logger.error(f"代理API返回错误。代码: {data.get('code')}，消息: {error_msg}")
        raise ProxyAPIError(f"API错误代码 {data.get('code')}: {error_msg}")

    proxy_list = data.get("data")
    if not proxy_list or not isinstance(proxy_list, list) or len(proxy_list) == 0:
        logger.warning("代理API返回成功代码，但无代理数据或列表为空。")
        raise NoAvailableProxyError("API未返回可用的代理IP数据。")

    # 假设我们总是取第一个，因为count=1
    first_proxy = proxy_list[0]
    if not all(k in first_proxy for k in ["ip", "port"]):
        logger.error(f"代理数据格式不正确。缺少 'ip' 或 'port'。数据: {first_proxy}")
        raise ProxyAPIError("代理数据格式不正确：缺少 'ip' 或 'port'。")

    logger.info(f"成功获取代理: {first_proxy['ip']}:{first_proxy['port']}")
    return {"ip": str(first_proxy["ip"]), "port": int(first_proxy["port"])}

# 示例用法 (可以放在测试代码中)
if __name__ == '__main__':
    # 请替换为您的有效API URL和Key进行测试
    # 注意：直接运行此脚本可能会产生API调用费用
    TEST_API_URL = "https://www.lthttp.com/iplist" # 请确保这是正确的API端点
    TEST_API_KEY = "4191e4d2d5698a59" # 请使用您的真实密钥

    # 模拟配置文件中的读取
    class Config:
        PROXY_API_URL = TEST_API_URL
        PROXY_API_KEY = TEST_API_KEY
        MAX_RETRIES = 2
        RETRY_DELAY_SECONDS = 2

    config = Config()

    # 更新装饰器参数以匹配config (实际应用中retry装饰器参数应从config读取或硬编码)
    # 这里为了简单起见，我们假设retry装饰器在get_proxy_ip定义时就已经设置好了
    # 或者，可以将配置传递给 retryable_get_proxy_ip

    logger.info("Attempting to get proxy IP...")
    try:
        # params可以从config.yaml中的其他代理参数构建
        proxy = get_proxy_ip(config.PROXY_API_URL, config.PROXY_API_KEY, params={"protocol":0, "type": 0})
        logger.info(f"Retrieved proxy: {proxy}")
    except NoAvailableProxyError:
        logger.error("No available proxy IPs after retries.")
    except ProxyAPIError as e:
        logger.error(f"Proxy API specific error: {e}")
    except requests.exceptions.RequestException as e:
        logger.error(f"Network related error during proxy fetching: {e}")
    except Exception as e:
        logger.error(f"An unexpected error occurred: {e}") 