import openai
from utils.logger import logger
from utils.decorators import retry

class OpenAIError(Exception):
    """自定义OpenAI API相关错误。"""
    pass

@retry(attempts=3, delay_seconds=10, accepted_exceptions=(openai.APIError, openai.APIConnectionError, openai.RateLimitError))
def get_openai_response(api_key: str, model: str, messages: list[dict], base_url: str = None, max_tokens: int = 150, temperature: float = 0.7) -> str:
    """
    调用OpenAI API获取聊天回复。

    :param api_key: OpenAI API密钥。
    :param model: 使用的模型，例如 "gpt-3.5-turbo"。
    :param messages: 发送给模型的对话历史列表，格式为 [{"role": "user/assistant", "content": "..."}, ...]。
    :param base_url: 可选，OpenAI API的基础URL。
    :param max_tokens: 生成回复的最大token数。
    :param temperature: 控制生成文本的随机性，0表示最确定性，1表示最随机。
    :return: OpenAI API返回的聊天回复文本。
    :raises OpenAIError: 如果API调用失败或返回错误。
    :raises openai.APIError: OpenAI库抛出的特定API错误，会被retry捕获。
    """
    if not api_key:
        # logger.error("OpenAI API key is not configured.") # 将在下一步统一翻译
        raise OpenAIError("OpenAI API key is missing.") # 异常消息保持英文或也翻译

    # 根据是否有 base_url 初始化 OpenAI 客户端
    if base_url and base_url.strip():
        # logger.info(f"使用自定义 OpenAI baseURL: {base_url}")
        client = openai.OpenAI(api_key=api_key, base_url=base_url)
    else:
        # logger.debug("使用默认 OpenAI baseURL。")
        # 如果不提供 base_url，则需要确保 openai.api_key 已设置，或者实例化时不依赖全局设置
        # 为了与旧版 openai < 1.0 的行为兼容 (依赖全局 openai.api_key)，我们先设置它
        openai.api_key = api_key 
        client = openai.OpenAI() # 将使用全局 openai.api_key (如果已设置) 或环境变量
                                 # 如果 openai.api_key 未在外部设置，这里会报错，所以上面一行 openai.api_key = api_key 很重要

    # 为日志准备消息预览
    first_message_content = "" 
    if messages and isinstance(messages, list) and len(messages) > 0 and isinstance(messages[0], dict):
        first_message_content = messages[0].get("content", "")
    elif isinstance(messages, str): # 兼容旧的单prompt字符串，尽管不推荐
        first_message_content = messages
        messages = [{"role": "user", "content": messages}] # 转换为消息列表格式
    else:
        # logger.error("Invalid format for messages argument in get_openai_response.")
        raise OpenAIError("Invalid messages format for OpenAI request.")

    try:
        log_message_preview = first_message_content[:100] if isinstance(first_message_content, str) else ""
        # logger.debug(f"Sending {len(messages)} messages to OpenAI model {model}. First message: '{log_message_preview}...'")
        
        response = client.chat.completions.create(
            model=model,
            messages=messages,
            max_tokens=max_tokens,
            temperature=temperature,
        )
        
        # logger.debug(f"OpenAI API raw response: {response}")

        if response.choices and len(response.choices) > 0:
            content = response.choices[0].message.content.strip()
            # logger.info(f"OpenAI response received: '{content[:100]}...'")
            return content
        else:
            # logger.error("OpenAI API returned no choices or empty response.")
            raise OpenAIError("OpenAI API returned no choices.")

    except openai.APIError as e:
        # logger.error(f"OpenAI API Error: {e} (Type: {type(e).__name__})")
        raise
    except Exception as e:
        # logger.error(f"An unexpected error occurred while calling OpenAI API: {e}")
        raise OpenAIError(f"Unexpected error: {e}") from e

# 示例用法 (保持英文供开发者测试)
if __name__ == '__main__':
    TEST_API_KEY = "" 
    TEST_MODEL = "gpt-3.5-turbo"
    TEST_BASE_URL = "https://ark.cn-beijing.volces.com/api/v3" # 示例，请替换

    # logger.info("Testing OpenAI API client...") # 此处及以下的测试日志保持英文
    if TEST_API_KEY == "78795ec7-d38d-4adb-a9eb-46aa6677b568":
        print("OpenAI API Key not provided in the example. Skipping live test.")
    else:
        print("Testing OpenAI API client with message list...")
        try:
            sample_messages = [
                {"role": "system", "content": "You are a helpful assistant answering questions about construction certifications."},
                {"role": "user", "content": "你好，我想咨询一下关于机电一级建造师的报考条件。"}
            ]
            # 测试使用 baseURL
            if TEST_BASE_URL == "https://ark.cn-beijing.volces.com/api/v3" or not TEST_BASE_URL:
                print("TEST_BASE_URL is a placeholder or empty. Testing without custom base URL first (if API key allows default)...")
                # 为了能独立测试无baseURL的情况，如果openai.api_key需要全局设置，确保它被设置
                openai.api_key = TEST_API_KEY 
                # ai_reply_default = get_openai_response(api_key=TEST_API_KEY, model=TEST_MODEL, messages=sample_messages)
                # print(f"AI Reply (default baseURL): {ai_reply_default}")
                # print("Skipping custom baseURL test as it's not properly set.")
            else:
                print(f"Testing with custom baseURL: {TEST_BASE_URL}")
                ai_reply_custom_base = get_openai_response(api_key=TEST_API_KEY, model=TEST_MODEL, messages=sample_messages, base_url=TEST_BASE_URL)
                print(f"AI Reply (custom baseURL): {ai_reply_custom_base}")

            sample_messages_2 = [
                {"role": "user", "content": "我应该如何准备考试？"}
            ]
            # ai_reply_2 = get_openai_response(TEST_API_KEY, TEST_MODEL, sample_messages_2, max_tokens=200, base_url=TEST_BASE_URL if (TEST_BASE_URL != "https://火山引擎的URL放这里" and TEST_BASE_URL) else None)
            # print(f"AI Reply to single new prompt (using potentially custom baseURL): {ai_reply_2}")

        except OpenAIError as e:
            print(f"OpenAI client test failed: {e}")
        except Exception as e:
            print(f"An unexpected error in OpenAI test: {e}") 