#!/usr/bin/env python3
"""
测试方法修复
"""

import sys
import yaml
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_method_existence():
    """测试方法是否存在"""
    logger.info("=== 测试方法存在性 ===")
    
    try:
        # 加载配置
        with open('config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 导入SystemCleanup类
        from utils.system_cleanup import SystemCleanup
        
        # 创建实例
        cleanup = SystemCleanup(config)
        
        # 检查关键方法是否存在
        required_methods = [
            '_smart_cleanup_long_running_chrome',
            '_should_cleanup_chrome_process',
            '_get_active_task_count',
            'perform_cleanup'
        ]
        
        for method_name in required_methods:
            if hasattr(cleanup, method_name):
                logger.info(f"✓ 方法 {method_name} 存在")
            else:
                logger.error(f"✗ 方法 {method_name} 不存在")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 测试方法存在性失败: {e}")
        return False

def test_method_call():
    """测试方法调用"""
    logger.info("=== 测试方法调用 ===")
    
    try:
        # 加载配置
        with open('config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 导入SystemCleanup类
        from utils.system_cleanup import SystemCleanup
        
        # 创建实例
        cleanup = SystemCleanup(config)
        
        # 测试智能清理方法
        logger.info("测试 _smart_cleanup_long_running_chrome 方法...")
        result = cleanup._smart_cleanup_long_running_chrome()
        logger.info(f"✓ _smart_cleanup_long_running_chrome 返回: {result}")
        
        # 测试活跃任务计数方法
        logger.info("测试 _get_active_task_count 方法...")
        count = cleanup._get_active_task_count()
        logger.info(f"✓ _get_active_task_count 返回: {count}")
        
        # 测试清理判断方法
        logger.info("测试 _should_cleanup_chrome_process 方法...")
        should_cleanup = cleanup._should_cleanup_chrome_process(30, 1, {'pid': 12345})
        logger.info(f"✓ _should_cleanup_chrome_process 返回: {should_cleanup}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 测试方法调用失败: {e}")
        return False

def test_perform_cleanup():
    """测试完整的清理流程"""
    logger.info("=== 测试完整清理流程 ===")
    
    try:
        # 加载配置
        with open('config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 导入SystemCleanup类
        from utils.system_cleanup import SystemCleanup
        
        # 创建实例
        cleanup = SystemCleanup(config)
        
        # 执行完整清理
        logger.info("执行 perform_cleanup 方法...")
        cleanup.perform_cleanup()
        logger.info("✓ perform_cleanup 执行成功")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 测试完整清理流程失败: {e}")
        return False

def test_force_cleanup():
    """测试强制清理函数"""
    logger.info("=== 测试强制清理函数 ===")
    
    try:
        from utils.system_cleanup import force_system_resource_cleanup
        
        logger.info("执行 force_system_resource_cleanup 函数...")
        result = force_system_resource_cleanup()
        logger.info(f"✓ force_system_resource_cleanup 返回: {result}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 测试强制清理函数失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("开始测试方法修复...")
    
    tests = [
        ("方法存在性", test_method_existence),
        ("方法调用", test_method_call),
        ("完整清理流程", test_perform_cleanup),
        ("强制清理函数", test_force_cleanup),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n--- 执行测试: {test_name} ---")
        try:
            result = test_func()
            if result:
                logger.info(f"✓ {test_name} 通过")
                passed += 1
            else:
                logger.error(f"✗ {test_name} 失败")
        except Exception as e:
            logger.error(f"✗ {test_name} 异常: {e}")
    
    logger.info(f"\n=== 测试结果 ===")
    logger.info(f"通过: {passed}/{total}")
    
    if passed == total:
        logger.info("🎉 所有测试通过！方法修复成功")
        return True
    elif passed >= total * 0.75:
        logger.info("✅ 大部分测试通过，修复基本成功")
        return True
    else:
        logger.error("❌ 多数测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
