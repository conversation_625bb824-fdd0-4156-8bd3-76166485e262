#!/usr/bin/env python3
"""
功能测试脚本 - 验证修改后的系统功能
测试浏览器功能、反检测能力和用户模拟效果
"""

import asyncio
import sys
from playwright.async_api import async_playwright
from utils.logger import logger
from utils.ua_generator import get_random_user_agent

class FunctionalityTest:
    def __init__(self):
        self.playwright = None
        self.browser = None
        self.context = None
        self.page = None
        
    async def run_all_tests(self):
        """运行所有功能测试"""
        logger.info("开始功能测试...")
        
        test_results = {
            'browser_launch': False,
            'user_agent': False,
            'javascript': False,
            'local_storage': False,
            'webgl_support': False,
            'media_support': False,
            'anti_detection': False,
            'network_request': False
        }
        
        try:
            # 1. 测试浏览器启动
            test_results['browser_launch'] = await self._test_browser_launch()
            
            if test_results['browser_launch']:
                # 2. 测试User-Agent
                test_results['user_agent'] = await self._test_user_agent()
                
                # 3. 测试JavaScript功能
                test_results['javascript'] = await self._test_javascript()
                
                # 4. 测试LocalStorage
                test_results['local_storage'] = await self._test_local_storage()
                
                # 5. 测试WebGL支持
                test_results['webgl_support'] = await self._test_webgl()
                
                # 6. 测试媒体支持
                test_results['media_support'] = await self._test_media_support()
                
                # 7. 测试反检测功能
                test_results['anti_detection'] = await self._test_anti_detection()
                
                # 8. 测试网络请求
                test_results['network_request'] = await self._test_network_request()
                
        except Exception as e:
            logger.error(f"测试过程中发生错误: {e}", exc_info=True)
        finally:
            await self._cleanup()
            
        # 输出测试结果
        self._print_results(test_results)
        return test_results
    
    async def _test_browser_launch(self):
        """测试浏览器启动"""
        try:
            logger.info("测试: 浏览器启动...")
            self.playwright = await async_playwright().start()
            
            # 使用与主程序相同的启动参数
            launch_args = [
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-setuid-sandbox',
                '--no-zygote',
                '--disable-gpu',
                '--disable-gpu-sandbox',
                '--disable-software-rasterizer',
                '--thread-pool-size=2',
                '--max-concurrent-runs=1',
                '--renderer-process-limit=1',
            ]
            
            launch_options = {
                'headless': True,
                'args': launch_args
            }
            
            self.browser = await self.playwright.chromium.launch(**launch_options)
            
            ua_string = get_random_user_agent('pc')
            context_options = {
                'user_agent': ua_string,
                'viewport': {'width': 1366, 'height': 768},
                'ignore_https_errors': True,
                'java_script_enabled': True,
            }
            
            self.context = await self.browser.new_context(**context_options)
            self.page = await self.context.new_page()
            
            logger.info("✅ 浏览器启动测试: 通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 浏览器启动测试: 失败 - {e}")
            return False
    
    async def _test_user_agent(self):
        """测试User-Agent设置"""
        try:
            logger.info("测试: User-Agent设置...")
            
            ua_from_page = await self.page.evaluate('navigator.userAgent')
            logger.info(f"页面User-Agent: {ua_from_page}")
            
            # 检查是否包含常见的浏览器标识
            if 'Mozilla' in ua_from_page and ('Chrome' in ua_from_page or 'Firefox' in ua_from_page):
                logger.info("✅ User-Agent测试: 通过")
                return True
            else:
                logger.warning("⚠️ User-Agent测试: 可疑")
                return False
                
        except Exception as e:
            logger.error(f"❌ User-Agent测试: 失败 - {e}")
            return False
    
    async def _test_javascript(self):
        """测试JavaScript功能"""
        try:
            logger.info("测试: JavaScript功能...")
            
            result = await self.page.evaluate('1 + 1')
            if result == 2:
                logger.info("✅ JavaScript测试: 通过")
                return True
            else:
                logger.error("❌ JavaScript测试: 计算错误")
                return False
                
        except Exception as e:
            logger.error(f"❌ JavaScript测试: 失败 - {e}")
            return False
    
    async def _test_local_storage(self):
        """测试LocalStorage功能"""
        try:
            logger.info("测试: LocalStorage功能...")
            
            # 尝试设置和读取localStorage
            await self.page.evaluate('localStorage.setItem("test", "value")')
            result = await self.page.evaluate('localStorage.getItem("test")')
            
            if result == "value":
                logger.info("✅ LocalStorage测试: 通过")
                return True
            else:
                logger.warning("⚠️ LocalStorage测试: 功能受限")
                return False
                
        except Exception as e:
            logger.warning(f"⚠️ LocalStorage测试: 失败 - {e}")
            return False
    
    async def _test_webgl(self):
        """测试WebGL支持"""
        try:
            logger.info("测试: WebGL支持...")
            
            webgl_support = await self.page.evaluate('''
                () => {
                    try {
                        const canvas = document.createElement('canvas');
                        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                        return !!gl;
                    } catch (e) {
                        return false;
                    }
                }
            ''')
            
            if webgl_support:
                logger.info("✅ WebGL测试: 支持")
                return True
            else:
                logger.warning("⚠️ WebGL测试: 不支持（可能被禁用）")
                return False
                
        except Exception as e:
            logger.warning(f"⚠️ WebGL测试: 失败 - {e}")
            return False
    
    async def _test_media_support(self):
        """测试媒体功能支持"""
        try:
            logger.info("测试: 媒体功能支持...")
            
            media_support = await self.page.evaluate('''
                () => {
                    const audio = document.createElement('audio');
                    const video = document.createElement('video');
                    return {
                        audio: !!audio.canPlayType,
                        video: !!video.canPlayType,
                        mediaDevices: !!navigator.mediaDevices
                    };
                }
            ''')
            
            logger.info(f"媒体支持状态: {media_support}")
            
            # 至少要有基本的音频/视频支持
            if media_support['audio'] and media_support['video']:
                logger.info("✅ 媒体支持测试: 通过")
                return True
            else:
                logger.warning("⚠️ 媒体支持测试: 部分功能受限")
                return False
                
        except Exception as e:
            logger.warning(f"⚠️ 媒体支持测试: 失败 - {e}")
            return False
    
    async def _test_anti_detection(self):
        """测试反检测功能"""
        try:
            logger.info("测试: 反检测功能...")
            
            # 添加反检测脚本
            await self.page.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
                
                window.chrome = {
                    runtime: {},
                };
                
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                });
            """)
            
            detection_results = await self.page.evaluate('''
                () => {
                    return {
                        webdriver: navigator.webdriver,
                        chrome: !!window.chrome,
                        plugins: navigator.plugins.length,
                        languages: navigator.languages.length > 0
                    };
                }
            ''')
            
            logger.info(f"检测结果: {detection_results}")
            
            # 检查反检测是否生效
            if (detection_results['webdriver'] is None and 
                detection_results['chrome'] and 
                detection_results['plugins'] > 0):
                logger.info("✅ 反检测测试: 通过")
                return True
            else:
                logger.warning("⚠️ 反检测测试: 可能被检测")
                return False
                
        except Exception as e:
            logger.error(f"❌ 反检测测试: 失败 - {e}")
            return False
    
    async def _test_network_request(self):
        """测试网络请求功能"""
        try:
            logger.info("测试: 网络请求功能...")
            
            # 尝试访问一个简单的网页
            response = await self.page.goto('https://httpbin.org/user-agent', 
                                           wait_until='networkidle', 
                                           timeout=10000)
            
            if response and response.ok:
                content = await self.page.content()
                if 'user-agent' in content.lower():
                    logger.info("✅ 网络请求测试: 通过")
                    return True
                    
            logger.warning("⚠️ 网络请求测试: 响应异常")
            return False
            
        except Exception as e:
            logger.warning(f"⚠️ 网络请求测试: 失败 - {e}")
            return False
    
    async def _cleanup(self):
        """清理资源"""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
        except Exception as e:
            logger.error(f"清理资源时出错: {e}")
    
    def _print_results(self, results):
        """打印测试结果"""
        print("\n" + "="*60)
        print("功能测试结果报告")
        print("="*60)
        
        total_tests = len(results)
        passed_tests = sum(1 for v in results.values() if v)
        
        for test_name, passed in results.items():
            status = "✅ 通过" if passed else "❌ 失败"
            print(f"{test_name:20} : {status}")
        
        print("-"*60)
        print(f"总计: {passed_tests}/{total_tests} 项测试通过")
        
        # 评估整体影响
        critical_tests = ['browser_launch', 'user_agent', 'javascript', 'anti_detection']
        critical_passed = sum(1 for test in critical_tests if results.get(test, False))
        
        print("\n影响评估:")
        if critical_passed == len(critical_tests):
            print("🟢 核心功能完全正常，修改对用户模拟影响很小")
        elif critical_passed >= len(critical_tests) * 0.75:
            print("🟡 核心功能基本正常，可能有轻微影响")
        else:
            print("🔴 核心功能受到影响，需要调整配置")
            
        print("="*60)

async def main():
    """主函数"""
    tester = FunctionalityTest()
    results = await tester.run_all_tests()
    
    # 根据测试结果返回退出码
    critical_tests = ['browser_launch', 'user_agent', 'javascript', 'anti_detection']
    critical_passed = sum(1 for test in critical_tests if results.get(test, False))
    
    if critical_passed == len(critical_tests):
        sys.exit(0)  # 成功
    elif critical_passed >= len(critical_tests) * 0.75:
        sys.exit(1)  # 部分问题
    else:
        sys.exit(2)  # 严重问题

if __name__ == "__main__":
    asyncio.run(main()) 