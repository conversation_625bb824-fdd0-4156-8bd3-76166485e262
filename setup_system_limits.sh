#!/bin/bash

# 系统限制设置脚本 - 解决资源耗尽问题
# 使用方法: sudo bash setup_system_limits.sh

echo "设置系统资源限制以解决Playwright资源耗尽问题..."

# 检查是否以root权限运行
if [ "$EUID" -ne 0 ]; then
    echo "请以root权限运行此脚本: sudo bash setup_system_limits.sh"
    exit 1
fi

# 1. 设置文件描述符限制
echo "设置文件描述符限制..."

# 设置系统级别的limits
if ! grep -q "* soft nofile 65536" /etc/security/limits.conf; then
    echo "* soft nofile 65536" >> /etc/security/limits.conf
fi

if ! grep -q "* hard nofile 65536" /etc/security/limits.conf; then
    echo "* hard nofile 65536" >> /etc/security/limits.conf
fi

# 设置进程数限制
if ! grep -q "* soft nproc 32768" /etc/security/limits.conf; then
    echo "* soft nproc 32768" >> /etc/security/limits.conf
fi

if ! grep -q "* hard nproc 32768" /etc/security/limits.conf; then
    echo "* hard nproc 32768" >> /etc/security/limits.conf
fi

# 2. 设置内核参数
echo "设置内核参数..."

# 设置最大打开文件数
echo "fs.file-max = 2097152" > /etc/sysctl.d/99-playwright-limits.conf

# 设置最大进程数
echo "kernel.pid_max = 4194303" >> /etc/sysctl.d/99-playwright-limits.conf

# 设置虚拟内存参数
echo "vm.max_map_count = 262144" >> /etc/sysctl.d/99-playwright-limits.conf

# 设置网络参数
echo "net.core.somaxconn = 65535" >> /etc/sysctl.d/99-playwright-limits.conf

# 应用内核参数
sysctl -p /etc/sysctl.d/99-playwright-limits.conf

# 3. 设置systemd服务限制（如果使用systemd）
echo "设置systemd服务限制..."

if systemctl --version >/dev/null 2>&1; then
    # 创建systemd配置目录
    mkdir -p /etc/systemd/system.conf.d/
    
    # 设置systemd默认限制
    cat > /etc/systemd/system.conf.d/playwright-limits.conf << 'EOF'
[Manager]
DefaultLimitNOFILE=65536
DefaultLimitNPROC=32768
DefaultLimitMEMLOCK=infinity
EOF

    # 重新加载systemd配置
    systemctl daemon-reload
fi

# 4. 为Docker容器设置限制（如果使用Docker）
echo "为Docker容器优化设置..."

if command -v docker >/dev/null 2>&1; then
    # 设置Docker daemon限制
    mkdir -p /etc/docker
    
    if [ ! -f /etc/docker/daemon.json ]; then
        echo '{}' > /etc/docker/daemon.json
    fi
    
    # 使用jq更新Docker配置（如果没有jq则手动创建）
    if command -v jq >/dev/null 2>&1; then
        jq '. + {"default-ulimits": {"nofile": {"Name": "nofile", "Hard": 65536, "Soft": 65536}, "nproc": {"Name": "nproc", "Hard": 32768, "Soft": 32768}}}' /etc/docker/daemon.json > /tmp/daemon.json.tmp
        mv /tmp/daemon.json.tmp /etc/docker/daemon.json
    else
        cat > /etc/docker/daemon.json << 'EOF'
{
  "default-ulimits": {
    "nofile": {
      "Name": "nofile",
      "Hard": 65536,
      "Soft": 65536
    },
    "nproc": {
      "Name": "nproc",
      "Hard": 32768,
      "Soft": 32768
    }
  }
}
EOF
    fi
    
    echo "请重启Docker服务以应用新配置: systemctl restart docker"
fi

# 5. 创建临时目录清理脚本
echo "创建临时目录清理脚本..."

cat > /usr/local/bin/cleanup-playwright-temp.sh << 'EOF'
#!/bin/bash
# Playwright临时文件清理脚本

echo "开始清理Playwright临时文件..."

# 清理/tmp目录中的playwright文件
find /tmp -name "*playwright*" -type d -mtime +0 -exec rm -rf {} + 2>/dev/null
find /tmp -name "*chromium*" -type d -mtime +0 -exec rm -rf {} + 2>/dev/null

# 清理/var/tmp目录
find /var/tmp -name "*playwright*" -type d -mtime +0 -exec rm -rf {} + 2>/dev/null
find /var/tmp -name "*chromium*" -type d -mtime +0 -exec rm -rf {} + 2>/dev/null

# 显示清理结果
echo "临时文件清理完成"
EOF

chmod +x /usr/local/bin/cleanup-playwright-temp.sh

# 6. 设置定时清理任务
echo "设置定时清理任务..."

# 每小时清理一次临时文件
crontab -l 2>/dev/null | grep -v "cleanup-playwright-temp" | crontab -
(crontab -l 2>/dev/null; echo "0 * * * * /usr/local/bin/cleanup-playwright-temp.sh >> /var/log/playwright-cleanup.log 2>&1") | crontab -

echo "系统限制设置完成！"
echo ""
echo "设置摘要:"
echo "- 文件描述符限制: 65536"
echo "- 进程数限制: 32768"
echo "- 内核文件最大数: 2097152"
echo "- 定时清理任务: 每小时执行"
echo ""
echo "重要提示:"
echo "1. 这些设置在下次重启后生效"
echo "2. 当前会话需要重新登录以应用limits.conf设置"
echo "3. 如果使用Docker，请重启Docker服务"
echo "4. 建议重启系统以确保所有设置生效"
echo ""
echo "验证设置是否生效:"
echo "ulimit -n  # 检查文件描述符限制"
echo "ulimit -u  # 检查进程数限制" 