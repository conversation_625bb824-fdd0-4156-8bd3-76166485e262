# 系统资源耗尽问题 - 深度修复指南

## 🚨 问题诊断更新

### 最新问题分析：
基于2025-06-11的运行日志，问题核心是：
- **线程创建失败**：`pthread_create: Resource temporarily unavailable (11)`
- **系统资源耗尽**：即使有自动清理，问题依然持续出现
- **清理效果不佳**：清理程序报告"清理了0个项目"，说明清理逻辑有缺陷

### 根本原因：
1. **Chromium进程线程泄漏**：每个浏览器实例创建大量线程，未完全释放
2. **浏览器实例重用失效**：没有按计划重启浏览器实例
3. **清理策略不够激进**：只清理僵尸进程，忽略活跃但占用资源的进程

## 🔧 深度修复措施（第二版）

### 1. 激进的浏览器参数优化
新增25+个严格的线程和进程限制参数：
```
--thread-pool-size=2
--max-concurrent-runs=1
--disable-threaded-animation
--disable-threaded-compositing
--disable-threaded-scrolling
--num-raster-threads=1
--max_old_space_size=2048  # 从4096降低到2048
```

### 2. 强制浏览器实例管理
- 浏览器使用计数器：每10个任务强制重启（从25降低到10）
- 自动检测资源状况：启动前检查系统资源
- 强制重启机制：遇到问题立即重启浏览器实例

### 3. 紧急清理系统
- **5分钟清理间隔**（从15分钟缩短）
- **1小时临时文件保留**（从3小时缩短）
- **主动进程终止**：清理运行超过30分钟的Chromium进程
- **资源临界检测**：内存>85%、文件描述符>300时触发紧急清理

### 4. 系统级限制加强
更新的阈值设置：
```yaml
MAX_CHROME_PROCESSES: 3              # 最大Chrome进程数（降低）
MAX_SYSTEM_MEMORY_PERCENT: 80        # 内存使用率阈值（降低）
MAX_OPEN_FILES_PER_PROCESS: 200      # 文件描述符限制（降低）
EMERGENCY_CLEANUP_ENABLED: true      # 启用紧急清理
THREAD_MONITORING_ENABLED: true      # 启用线程监控
```

### 5. 独立紧急监控系统
新增 `emergency_monitor.py`：
- **30秒实时监控**系统资源
- **自动重启**检测到严重问题时
- **智能限频**：1小时内最多重启3次
- **多层保护**：内存、线程、文件描述符、进程数量

## 🚀 紧急部署步骤

### 立即执行（服务器上）：

```bash
# 1. 停止当前程序
pkill -f "python.*main.py"
sleep 3

# 2. 强制清理所有相关进程
pkill -9 -f chromium
pkill -9 -f chrome
pkill -9 -f playwright

# 3. 清理临时文件
sudo find /tmp -name "*playwright*" -type d -exec rm -rf {} + 2>/dev/null
sudo find /tmp -name "*chromium*" -type d -exec rm -rf {} + 2>/dev/null

# 4. 安装依赖（如果还没有）
pip install psutil>=5.8,<6.0

# 5. 运行系统配置脚本
sudo bash setup_system_limits.sh

# 6. 启动紧急监控（后台运行）
nohup python emergency_monitor.py > emergency_monitor.log 2>&1 &

# 7. 重启主程序
python main.py
```

### 验证部署：

```bash
# 检查系统限制
ulimit -n  # 应显示65536
ulimit -u  # 应显示32768

# 检查监控程序
ps aux | grep emergency_monitor

# 检查日志
tail -f emergency_monitor.log
tail -f chatbot_automation.log
```

## 📊 监控指标（更新）

### 关键成功指标：
- ✅ **浏览器启动成功率 > 95%**
- ✅ **系统Chrome进程数 ≤ 3个**
- ✅ **主进程线程数 ≤ 50个**
- ✅ **文件描述符数 ≤ 200个**
- ✅ **系统内存使用率 ≤ 80%**

### 新增监控日志：
```
✅ 正常运行：
- "当前进程线程数: XX"
- "系统总线程数: XX, Chrome进程数: X"
- "强制系统资源清理完成，清理了 X 个项目"

⚠️ 警告信号：
- "检测到系统问题: Chrome进程过多"
- "内存使用率过高: XX%"
- "主进程线程过多: XX"

🚨 紧急情况：
- "触发紧急清理模式！"
- "检测到严重问题，将重启主程序"
- "一小时内重启次数已达上限"
```

## 🔄 运维策略

### 实时监控命令：
```bash
# 实时资源监控
watch -n 5 'echo "=== 进程统计 ==="; ps aux | grep -E "(chrome|python)" | wc -l; echo "=== 内存使用 ==="; free -h; echo "=== 文件描述符 ==="; lsof | wc -l'

# 检查线程数
ps -eLf | grep python | wc -l

# 检查Chrome进程详情
ps aux | grep chrome | grep -v grep
```

### 日常维护清单：
1. **每小时检查**：监控程序是否正常运行
2. **每天检查**：资源使用趋势和清理效果
3. **每周检查**：系统限制设置是否生效
4. **紧急时刻**：手动运行强制清理

## 📞 故障排除升级版

### 问题持续存在时：

```bash
# 1. 立即诊断
python -c "
import psutil
proc = psutil.Process()
print(f'线程数: {proc.num_threads()}')
print(f'文件描述符: {len(proc.open_files())}')
print(f'内存: {proc.memory_info().rss // 1024 // 1024}MB')
"

# 2. 系统级诊断
echo "=== 系统限制 ==="
ulimit -a
echo "=== 进程统计 ==="
ps aux | head -1; ps aux | grep -E "(chrome|python)" | head -10
echo "=== 内存状态 ==="
cat /proc/meminfo | grep -E "(MemTotal|MemAvailable|MemFree)"

# 3. 核选项：重启系统
sudo reboot  # 只在万不得已时使用
```

### 性能调优建议：
1. **考虑容器化部署**：使用Docker限制资源使用
2. **分布式部署**：多台服务器分担负载
3. **定时重启策略**：每24小时自动重启一次
4. **资源预留**：为系统保留至少20%的资源

## 📈 预期效果

实施深度修复后，应该观察到：

1. **显著减少错误**：
   - 不再出现 "pthread_create: Resource temporarily unavailable"
   - 不再出现 "Too many open files"
   - 浏览器启动成功率提升到95%以上

2. **资源使用稳定**：
   - 系统内存使用率保持在80%以下
   - Chrome进程数量控制在3个以内
   - 文件描述符数量保持在200以下

3. **自动恢复能力**：
   - 检测到问题时自动清理和重启
   - 有效防止资源泄漏累积
   - 降低人工干预需求

---

**重要提醒**：
- 此次修复采用了极其激进的资源管理策略
- 如果问题仍然存在，建议考虑硬件升级或架构重构
- 监控程序会自动处理大部分问题，但仍需定期人工检查
- 在生产环境中，建议先在测试环境验证所有修改