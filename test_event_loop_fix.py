#!/usr/bin/env python3
"""
测试事件循环修复效果
验证独立Playwright实例是否能正常工作
"""

import asyncio
import logging
from task_runner import TaskRunner

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_independent_playwright():
    """测试独立Playwright实例"""
    logger.info("开始测试独立Playwright实例...")
    
    # 模拟配置
    config = {
        'HEADLESS_BROWSER': True,
        'PROXY_API_URL': 'http://example.com/proxy',
        'PROXY_API_KEY': 'test_key',
        'USER_AGENT_API_URL': 'http://example.com/ua',
        'OPENAI_API_KEY': 'test_openai_key',
        'MAX_INTERACTION_COUNT': 3,
        'TASK_TIMEOUT_SECONDS': 450,
        'BROWSER_RESTART_INTERVAL_TASKS': 5,
        'MAX_CHROME_PROCESSES': 2,
        'AUTO_CLEANUP_ENABLED': True,
        'MAX_CHROME_RUNTIME_MINUTES': 30
    }
    
    # 创建TaskRunner实例（不传入Playwright实例）
    task_runner = TaskRunner(
        playwright=None,  # 让它创建独立实例
        config=config,
        task_id=999,
        should_perform_chat_interaction=False
    )
    
    try:
        # 测试浏览器初始化
        logger.info("测试浏览器初始化...")
        await task_runner._initialize_browser()
        logger.info("✅ 浏览器初始化成功")
        
        # 测试页面创建
        if task_runner.browser:
            logger.info("测试页面创建...")
            context = await task_runner.browser.new_context()
            page = await context.new_page()
            logger.info("✅ 页面创建成功")
            
            # 简单测试
            await page.goto("data:text/html,<html><body><h1>Test</h1></body></html>")
            title = await page.title()
            logger.info(f"✅ 页面访问成功，标题: {title}")
            
            # 清理
            await page.close()
            await context.close()
            logger.info("✅ 页面和上下文清理成功")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return False
    finally:
        # 清理资源
        try:
            await task_runner._cleanup()
            logger.info("✅ 资源清理成功")
        except Exception as e:
            logger.error(f"⚠️ 清理时出错: {e}")
    
    logger.info("✅ 独立Playwright实例测试完成")
    return True

def main():
    """主函数"""
    logger.info("开始事件循环修复验证...")
    
    # 运行测试
    success = asyncio.run(test_independent_playwright())
    
    if success:
        logger.info("🎉 事件循环修复验证成功！")
        logger.info("现在可以安全地启动主程序了")
    else:
        logger.error("❌ 事件循环修复验证失败")
    
    return success

if __name__ == "__main__":
    main()
