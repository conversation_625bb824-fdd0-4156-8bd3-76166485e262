# Placeholder for utils/decorators.py 
import time
import functools
from utils.logger import logger # 导入我们刚刚创建的logger
import asyncio

def retry(attempts=2, delay_seconds=5, accepted_exceptions=None):
    """
    一个通用的重试装饰器。

    :param attempts: 最大尝试次数。
    :param delay_seconds: 每次重试之间的延迟秒数。
    :param accepted_exceptions: 一个异常类型或元组，只有这些类型的异常会触发重试。
                                 如果为None，则所有Exception的子类都会触发重试。
    """
    if accepted_exceptions is None:
        accepted_exceptions = (Exception,)
    elif not isinstance(accepted_exceptions, tuple):
        accepted_exceptions = (accepted_exceptions,)

    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(1, attempts + 1):
                try:
                    return func(*args, **kwargs)
                except accepted_exceptions as e:
                    logger.warning(
                        f"函数 {func.__name__} 第 {attempt}/{attempts} 次尝试失败，原因：{type(e).__name__}: {e}。将在 {delay_seconds} 秒后重试..."
                    )
                    last_exception = e
                    time.sleep(delay_seconds)
                except Exception as e: # 捕获未在accepted_exceptions中定义的其他意外异常
                    logger.error(
                        f"函数 {func.__name__} 第 {attempt}/{attempts} 次尝试时发生意外错误：{type(e).__name__}: {e}。此错误类型不会触发重试。"
                    )
                    raise # 重新抛出非预期内的异常
            
            if last_exception:
                logger.error(f"函数 {func.__name__} 所有 {attempts} 次尝试均失败。最后异常: {last_exception}")
                raise last_exception
            return None 
        return wrapper
    return decorator

def retry_async(attempts=2, delay_seconds=5, accepted_exceptions=None, logger_instance=None):
    """
    一个通用的异步重试装饰器。

    :param attempts: 最大尝试次数。
    :param delay_seconds: 每次重试之间的延迟秒数。
    :param accepted_exceptions: 一个异常类型或元组，只有这些类型的异常会触发重试。
                                 如果为None，则所有Exception的子类都会触发重试。
    :param logger_instance: 用于日志记录的日志器实例。如果为None，则使用全局logger。
    """
    final_logger = logger_instance if logger_instance else logger

    if accepted_exceptions is None:
        accepted_exceptions = (Exception,)
    elif not isinstance(accepted_exceptions, tuple):
        accepted_exceptions = (accepted_exceptions,)

    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(1, attempts + 1):
                try:
                    return await func(*args, **kwargs)
                except accepted_exceptions as e:
                    final_logger.warning(
                        f"异步函数 {func.__name__} 第 {attempt}/{attempts} 次尝试失败，原因：{type(e).__name__}: {e}。将在 {delay_seconds} 秒后重试..."
                    )
                    last_exception = e
                    await asyncio.sleep(delay_seconds) # 使用 asyncio.sleep
                except Exception as e: # 捕获未在accepted_exceptions中定义的其他意外异常
                    final_logger.error(
                        f"异步函数 {func.__name__} 第 {attempt}/{attempts} 次尝试时发生意外错误：{type(e).__name__}: {e}。此错误类型不会触发重试。"
                    )
                    raise # 重新抛出非预期内的异常
            
            if last_exception:
                final_logger.error(f"异步函数 {func.__name__} 所有 {attempts} 次尝试均失败。最后异常: {last_exception}")
                raise last_exception
            return None # 或者根据情况返回一个特定的值或None
        return wrapper
    return decorator

# Example usage (developer testing logs remain in English)
if __name__ == '__main__':
    @retry(attempts=3, delay_seconds=1, accepted_exceptions=(ValueError, KeyError))
    def might_fail(fail_times):
        if might_fail.current_attempt < fail_times:
            might_fail.current_attempt += 1
            logger.info(f"might_fail attempt {might_fail.current_attempt}")
            if might_fail.current_attempt % 2 == 0:
                raise ValueError("Simulated ValueError")
            else:
                raise KeyError("Simulated KeyError")
        return "Success!"
    might_fail.current_attempt = 0

    @retry(attempts=2, delay_seconds=1)
    def might_fail_all_exceptions(fail_times):
        if might_fail_all_exceptions.current_attempt < fail_times:
            might_fail_all_exceptions.current_attempt += 1
            logger.info(f"might_fail_all_exceptions attempt {might_fail_all_exceptions.current_attempt}")
            raise ConnectionError("Simulated ConnectionError")
        return "Success!"
    might_fail_all_exceptions.current_attempt = 0

    try:
        logger.info("Testing retry with specific exceptions...")
        result = might_fail(2) 
        logger.info(f"Result: {result}")
    except Exception as e:
        logger.error(f"Test 1 Main error: {e}")

    try:
        logger.info("\nTesting retry with all exceptions...")
        result_all = might_fail_all_exceptions(3) 
        logger.info(f"Result: {result_all}")
    except Exception as e:
        logger.error(f"Test 2 Main error: {e}")

    @retry(attempts=2, delay_seconds=1, accepted_exceptions=ValueError)
    def non_retryable_error():
        logger.info("Testing non-retryable error...")
        raise TypeError("This error should not be retried by this decorator configuration.")
    
    try:
        non_retryable_error()
    except TypeError as e:
        logger.info(f"Successfully caught non-retryable error: {e}")
    except Exception as e:
        logger.error(f"Test 3 Main error: {e}") 