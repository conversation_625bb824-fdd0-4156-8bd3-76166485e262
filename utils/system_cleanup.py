import os
import time
import signal
import subprocess
import threading
from pathlib import Path
from utils.logger import logger

class SystemCleanup:
    """系统资源清理工具，用于处理长期运行的Playwright任务产生的资源积累问题"""
    
    def __init__(self, config: dict):
        self.config = config
        self.cleanup_interval = config.get('SYSTEM_CLEANUP_INTERVAL_MINUTES', 30)  # 默认30分钟清理一次
        self.max_temp_files_age_hours = config.get('MAX_TEMP_FILES_AGE_HOURS', 6)  # 6小时以上的临时文件被清理
        self.running = False
        self.cleanup_thread = None

        # 新增：紧急清理配置
        self.emergency_cleanup_enabled = config.get('EMERGENCY_CLEANUP_ENABLED', True)
        self.max_chrome_processes = config.get('MAX_CHROME_PROCESSES', 5)
        self.max_system_memory_percent = config.get('MAX_SYSTEM_MEMORY_PERCENT', 85)
        self.max_open_files_per_process = config.get('MAX_OPEN_FILES_PER_PROCESS', 300)
        self.thread_monitoring_enabled = config.get('THREAD_MONITORING_ENABLED', True)

        # 自动清理增强配置 - 更保守的设置
        self.auto_cleanup_enabled = config.get('AUTO_CLEANUP_ENABLED', True)
        self.auto_cleanup_interval_minutes = config.get('AUTO_CLEANUP_INTERVAL_MINUTES', 10)
        self.max_chrome_runtime_minutes = config.get('MAX_CHROME_RUNTIME_MINUTES', 45)  # 增加到45分钟

        # 新增：任务感知清理配置
        self.task_aware_cleanup = config.get('TASK_AWARE_CLEANUP', True)
        self.min_chrome_runtime_minutes = config.get('MIN_CHROME_RUNTIME_MINUTES', 20)  # 最小运行时间保护
        self.active_task_protection = config.get('ACTIVE_TASK_PROTECTION', True)  # 活跃任务保护
        
    def start_cleanup_daemon(self):
        """启动清理守护线程"""
        if self.running:
            logger.warning("系统清理守护进程已在运行")
            return
            
        self.running = True
        self.cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self.cleanup_thread.start()
        logger.info(f"系统清理守护进程已启动，清理间隔: {self.cleanup_interval}分钟")
        
    def stop_cleanup_daemon(self):
        """停止清理守护线程"""
        self.running = False
        if self.cleanup_thread and self.cleanup_thread.is_alive():
            self.cleanup_thread.join(timeout=5)
        logger.info("系统清理守护进程已停止")
        
    def _cleanup_loop(self):
        """清理循环"""
        while self.running:
            try:
                self.perform_cleanup()
            except Exception as e:
                logger.error(f"系统清理过程中发生错误: {e}", exc_info=True)
            
            # 分段睡眠，以便快速响应停止信号
            sleep_duration = self.cleanup_interval * 60  # 转换为秒
            sleep_start = time.time()
            while self.running and (time.time() - sleep_start) < sleep_duration:
                time.sleep(1)
                
    def perform_cleanup(self):
        """执行清理操作 - 改进版本，更加温和和智能"""
        logger.info("开始系统清理...")

        cleaned_count = 0

        # 0. 检查是否需要紧急清理（仅在真正紧急时触发）
        if self.emergency_cleanup_enabled:
            emergency_triggered = self._check_emergency_conditions()
            if emergency_triggered:
                logger.warning("触发紧急清理模式！")
                cleaned_count += self._perform_emergency_cleanup()
                return cleaned_count  # 紧急清理后直接返回，避免重复清理

        # 1. 清理临时目录中的playwright相关文件（温和清理）
        cleaned_count += self._cleanup_playwright_temp_files()

        # 2. 清理僵尸进程（仅清理真正的僵尸进程）
        cleaned_count += self._cleanup_zombie_processes()

        # 3. 检查并记录系统资源使用情况
        self._log_system_resources()

        # 4. 线程监控（如果启用）
        if self.thread_monitoring_enabled:
            self._monitor_threads()

        # 5. 智能清理长时间运行的Chrome进程（任务感知）
        if self.auto_cleanup_enabled:
            cleaned_count += self._smart_cleanup_long_running_chrome()

        logger.info(f"系统清理完成，共清理了 {cleaned_count} 个项目")
        
    def _check_emergency_conditions(self):
        """检查是否满足紧急清理条件"""
        try:
            import psutil
            
            # 检查内存使用率
            memory_info = psutil.virtual_memory()
            if memory_info.percent > self.max_system_memory_percent:
                logger.warning(f"内存使用率过高: {memory_info.percent}% > {self.max_system_memory_percent}%")
                return True
                
            # 检查Chrome进程数量
            chrome_count = 0
            for proc in psutil.process_iter(['name']):
                try:
                    if 'chrome' in proc.info['name'].lower():
                        chrome_count += 1
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                    
            if chrome_count > self.max_chrome_processes:
                logger.warning(f"Chrome进程数量过多: {chrome_count} > {self.max_chrome_processes}")
                return True
                
            # 检查当前进程的文件描述符数量
            current_process = psutil.Process()
            open_files_count = len(current_process.open_files())
            if open_files_count > self.max_open_files_per_process:
                logger.warning(f"文件描述符数量过多: {open_files_count} > {self.max_open_files_per_process}")
                return True
                
        except ImportError:
            logger.debug("psutil不可用，跳过紧急条件检查")
            
        return False
        
    def _perform_emergency_cleanup(self):
        """执行紧急清理"""
        logger.warning("执行紧急系统清理...")
        cleaned_count = 0
        
        # 强制清理所有Chromium进程
        cleaned_count += _cleanup_all_chromium_processes()
        
        # 强制垃圾收集
        import gc
        gc.collect()
        
        # 清理所有临时文件（不考虑时间限制）
        cleaned_count += self._emergency_cleanup_temp_files()
        
        logger.warning(f"紧急清理完成，清理了 {cleaned_count} 个项目")
        return cleaned_count
        
    def _emergency_cleanup_temp_files(self):
        """紧急清理所有相关临时文件"""
        cleaned_count = 0
        temp_dirs = ['/tmp', '/var/tmp']
        
        if os.name == 'posix' and 'darwin' in os.uname().sysname.lower():
            temp_dirs.append(os.path.expanduser('~/tmp'))
            temp_dirs.append('/private/tmp')
        
        for temp_dir in temp_dirs:
            if not os.path.exists(temp_dir):
                continue
                
            try:
                for item in os.listdir(temp_dir):
                    if ('playwright' in item.lower() or 
                        'chromium' in item.lower() or 
                        'chrome' in item.lower()):
                        item_path = os.path.join(temp_dir, item)
                        try:
                            if os.path.isdir(item_path):
                                import shutil
                                shutil.rmtree(item_path, ignore_errors=True)
                            else:
                                os.remove(item_path)
                            cleaned_count += 1
                        except (OSError, PermissionError):
                            pass
            except (OSError, PermissionError):
                pass
                
        return cleaned_count
        
    def _monitor_threads(self):
        """监控线程使用情况"""
        try:
            import psutil
            
            current_process = psutil.Process()
            thread_count = current_process.num_threads()
            
            logger.info(f"当前进程线程数: {thread_count}")
            
            # 如果线程数过多，发出警告
            if thread_count > 50:
                logger.warning(f"当前进程线程数过多: {thread_count}，可能存在线程泄漏")
                
            # 记录系统总线程数
            total_threads = 0
            for proc in psutil.process_iter(['num_threads']):
                try:
                    total_threads += proc.info.get('num_threads', 0)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                    
            logger.info(f"系统总线程数: {total_threads}")
            
        except ImportError:
            logger.debug("psutil不可用，跳过线程监控")
        
    def _cleanup_playwright_temp_files(self):
        """清理Playwright临时文件"""
        cleaned_count = 0
        temp_dirs = ['/tmp', '/var/tmp']
        
        # 如果是macOS，也检查用户临时目录
        if os.name == 'posix' and 'darwin' in os.uname().sysname.lower():
            temp_dirs.append(os.path.expanduser('~/tmp'))
            temp_dirs.append('/private/tmp')
        
        current_time = time.time()
        max_age_seconds = self.max_temp_files_age_hours * 3600
        
        for temp_dir in temp_dirs:
            if not os.path.exists(temp_dir):
                continue
                
            try:
                for item in os.listdir(temp_dir):
                    if 'playwright' in item.lower() or 'chromium' in item.lower():
                        item_path = os.path.join(temp_dir, item)
                        try:
                            # 检查文件/目录的修改时间
                            mtime = os.path.getmtime(item_path)
                            if current_time - mtime > max_age_seconds:
                                if os.path.isdir(item_path):
                                    import shutil
                                    shutil.rmtree(item_path)
                                    logger.debug(f"已清理临时目录: {item_path}")
                                else:
                                    os.remove(item_path)
                                    logger.debug(f"已清理临时文件: {item_path}")
                                cleaned_count += 1
                        except (OSError, PermissionError) as e:
                            logger.debug(f"无法清理 {item_path}: {e}")
            except (OSError, PermissionError) as e:
                logger.debug(f"无法访问临时目录 {temp_dir}: {e}")
                
        return cleaned_count
        
    def _cleanup_zombie_processes(self):
        """清理僵尸进程"""
        cleaned_count = 0
        try:
            # 查找可能的僵尸chromium进程
            result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'chromium' in line.lower() and '<defunct>' in line:
                        # 尝试提取进程ID并终止
                        parts = line.split()
                        if len(parts) > 1:
                            try:
                                pid = int(parts[1])
                                os.kill(pid, signal.SIGKILL)
                                logger.info(f"已终止僵尸进程 PID: {pid}")
                                cleaned_count += 1
                            except (ValueError, ProcessLookupError, PermissionError):
                                pass
        except Exception as e:
            logger.debug(f"清理僵尸进程时出错: {e}")
            
        return cleaned_count
        
    def _log_system_resources(self):
        """记录系统资源使用情况"""
        try:
            # 尝试使用psutil获取详细信息
            try:
                import psutil
                
                # 内存使用情况
                memory_info = psutil.virtual_memory()
                logger.info(f"内存使用情况: {memory_info.percent:.1f}% (可用: {memory_info.available // (1024*1024):.0f}MB)")
                
                # CPU使用情况
                cpu_percent = psutil.cpu_percent(interval=1)
                logger.info(f"CPU使用率: {cpu_percent:.1f}%")
                
                # 磁盘使用情况
                disk_info = psutil.disk_usage('/')
                logger.info(f"磁盘使用率: {disk_info.percent:.1f}%")
                
                # 进程信息
                current_process = psutil.Process()
                process_info = current_process.as_dict(attrs=['pid', 'memory_info', 'num_fds', 'num_threads'])
                logger.info(f"当前进程: PID={process_info['pid']}, 内存={process_info['memory_info'].rss // (1024*1024):.0f}MB, "
                           f"文件描述符={process_info.get('num_fds', 'N/A')}, 线程数={process_info['num_threads']}")
                
                # 检查chromium进程
                chromium_processes = []
                for proc in psutil.process_iter(['pid', 'name', 'memory_info']):
                    try:
                        if 'chrome' in proc.info['name'].lower() or 'chromium' in proc.info['name'].lower():
                            chromium_processes.append(proc.info)
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
                
                if chromium_processes:
                    total_chrome_memory = sum(p['memory_info'].rss for p in chromium_processes) // (1024*1024)
                    logger.info(f"Chromium进程: {len(chromium_processes)}个, 总内存: {total_chrome_memory}MB")
                    
                    # 如果chromium进程过多，记录警告
                    if len(chromium_processes) > 10:
                        logger.warning(f"检测到过多Chromium进程({len(chromium_processes)}个)，可能存在进程泄漏")
                        
            except ImportError:
                # 回退到原有的方法
                if os.name == 'posix':
                    if 'darwin' in os.uname().sysname.lower():
                        # macOS
                        result = subprocess.run(['vm_stat'], capture_output=True, text=True)
                        if result.returncode == 0:
                            logger.debug(f"内存状态: {result.stdout.split('Pages free:')[1].split('.')[0].strip()} 页面空闲")
                    else:
                        # Linux
                        with open('/proc/meminfo', 'r') as f:
                            meminfo = f.read()
                            for line in meminfo.split('\n'):
                                if 'MemAvailable:' in line:
                                    available_kb = int(line.split()[1])
                                    available_mb = available_kb / 1024
                                    logger.debug(f"可用内存: {available_mb:.0f} MB")
                                    break
                
                # 检查进程数量
                result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
                if result.returncode == 0:
                    process_count = len(result.stdout.split('\n')) - 1  # 减去标题行
                    chromium_count = result.stdout.lower().count('chromium')
                    logger.debug(f"总进程数: {process_count}, Chromium进程数: {chromium_count}")
                
        except Exception as e:
            logger.debug(f"获取系统资源信息时出错: {e}")

    def _smart_cleanup_long_running_chrome(self):
        """智能清理长时间运行的Chrome进程 - 任务感知版本"""
        cleaned_count = 0
        try:
            import psutil
            import time

            current_time = time.time()
            active_tasks = self._get_active_task_count()

            logger.debug(f"检查Chrome进程清理，当前活跃任务数: {active_tasks}")

            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time']):
                try:
                    proc_info = proc.info
                    if not proc_info['name']:
                        continue

                    # 检查是否是Chrome相关进程
                    if 'chrome' in proc_info['name'].lower():
                        create_time = proc_info.get('create_time', current_time)
                        runtime_minutes = (current_time - create_time) / 60

                        # 智能清理策略
                        should_cleanup = self._should_cleanup_chrome_process(
                            runtime_minutes, active_tasks, proc_info
                        )

                        if should_cleanup:
                            logger.info(f"智能清理Chrome进程: PID={proc_info['pid']}, "
                                      f"运行时间={runtime_minutes:.1f}分钟, 活跃任务数={active_tasks}")
                            try:
                                proc = psutil.Process(proc_info['pid'])
                                proc.terminate()
                                proc.wait(timeout=5)  # 给更多时间优雅退出
                                cleaned_count += 1
                            except psutil.TimeoutExpired:
                                # 只有在优雅退出失败时才强制终止
                                logger.warning(f"Chrome进程 {proc_info['pid']} 优雅退出失败，强制终止")
                                proc.kill()
                                cleaned_count += 1
                            except (psutil.NoSuchProcess, psutil.AccessDenied):
                                continue

                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
                except Exception as e:
                    logger.debug(f"检查Chrome进程时出错: {e}")

        except ImportError:
            logger.debug("psutil未安装，跳过智能Chrome清理")
        except Exception as e:
            logger.error(f"智能清理Chrome进程时出错: {e}")

        if cleaned_count > 0:
            logger.info(f"智能清理了 {cleaned_count} 个Chrome进程")

        return cleaned_count

    def _should_cleanup_chrome_process(self, runtime_minutes, active_tasks, proc_info):
        """判断是否应该清理Chrome进程"""
        # 基本保护：运行时间太短的进程不清理
        if runtime_minutes < self.min_chrome_runtime_minutes:
            return False

        # 如果有活跃任务且启用了任务保护，更加保守
        if self.active_task_protection and active_tasks > 0:
            # 有活跃任务时，只清理运行时间非常长的进程
            return runtime_minutes > (self.max_chrome_runtime_minutes * 1.5)

        # 没有活跃任务时，按正常策略清理
        return runtime_minutes > self.max_chrome_runtime_minutes

    def _get_active_task_count(self):
        """获取当前活跃任务数量的估算"""
        try:
            import psutil
            # 通过检查Python进程中包含task_runner的进程数来估算活跃任务
            active_count = 0
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    proc_info = proc.info
                    if proc_info['name'] and 'python' in proc_info['name'].lower():
                        cmdline = ' '.join(proc_info.get('cmdline', []))
                        if 'task_runner' in cmdline or 'chatbot_automation' in cmdline:
                            active_count += 1
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            return max(0, active_count - 1)  # 减去主进程
        except:
            return 0

def force_cleanup_playwright_processes():
    """强制清理所有Playwright相关进程 - 在严重错误时使用"""
    logger.warning("执行强制Playwright进程清理...")
    cleaned_count = 0
    
    try:
        # 查找并终止所有chromium相关进程
        result = subprocess.run(['pgrep', '-f', 'chromium'], capture_output=True, text=True)
        if result.returncode == 0:
            pids = result.stdout.strip().split('\n')
            for pid_str in pids:
                if pid_str.strip():
                    try:
                        pid = int(pid_str.strip())
                        os.kill(pid, signal.SIGTERM)
                        time.sleep(0.5)  # 给进程一些时间优雅退出
                        try:
                            os.kill(pid, signal.SIGKILL)  # 强制终止
                        except ProcessLookupError:
                            pass  # 进程已经退出
                        logger.info(f"强制终止进程 PID: {pid}")
                        cleaned_count += 1
                    except (ValueError, ProcessLookupError, PermissionError):
                        pass
                        
    except Exception as e:
        logger.error(f"强制清理进程时出错: {e}")
        
    logger.info(f"强制清理完成，终止了 {cleaned_count} 个进程")
    return cleaned_count

def force_system_resource_cleanup():
    """温和的系统资源清理 - 改进版本，避免干扰正常任务"""
    logger.info("执行系统资源清理...")  # 改为INFO级别
    cleaned_count = 0

    try:
        # 强制垃圾收集
        import gc
        gc.collect()

        # 检查系统资源状态，决定清理策略
        is_critical = _is_system_resource_critical()

        if is_critical:
            logger.warning("系统资源处于临界状态，执行紧急清理")
            # 只有在真正紧急时才执行激进清理
            cleaned_count += _cleanup_all_chromium_processes()
            cleaned_count += _emergency_chrome_cleanup()
        else:
            logger.info("系统资源正常，执行温和清理")
            # 正常情况下清理僵尸进程和Chrome相关僵尸进程
            cleaned_count += _cleanup_zombie_processes_only()
            cleaned_count += _cleanup_chrome_related_zombies()

        # 清理临时文件（总是安全的）
        cleaned_count += _cleanup_temp_files()
        cleaned_count += _cleanup_playwright_temp_dirs()

        # 记录文件描述符状态
        try:
            import psutil
            current_process = psutil.Process()
            open_files = current_process.open_files()
            logger.info(f"当前进程打开文件数量: {len(open_files)}")

            # 只有在文件数量异常多时才警告
            if len(open_files) > 500:  # 提高阈值
                logger.warning(f"检测到异常多的打开文件({len(open_files)}个)")
                _log_detailed_system_status()

        except ImportError:
            pass

    except Exception as e:
        logger.error(f"系统资源清理时出错: {e}")

    logger.info(f"系统资源清理完成，清理了 {cleaned_count} 个项目")
    return cleaned_count

def _cleanup_zombie_processes_only():
    """清理僵尸进程 - 修复版本"""
    cleaned_count = 0
    zombie_pids = []

    try:
        import psutil
        import os
        import signal

        # 首先收集所有僵尸进程信息
        for proc in psutil.process_iter(['pid', 'name', 'status', 'ppid']):
            try:
                proc_info = proc.info
                if proc_info['status'] == psutil.STATUS_ZOMBIE:
                    zombie_pids.append({
                        'pid': proc_info['pid'],
                        'name': proc_info.get('name', 'unknown'),
                        'ppid': proc_info.get('ppid', 0)
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue

        if not zombie_pids:
            logger.debug("未发现僵尸进程")
            return 0

        logger.info(f"发现 {len(zombie_pids)} 个僵尸进程")

        # 尝试通知父进程清理僵尸子进程
        parent_pids = set()
        for zombie in zombie_pids:
            if zombie['ppid'] > 1:  # 排除init进程
                parent_pids.add(zombie['ppid'])

        # 向父进程发送SIGCHLD信号，提示清理僵尸子进程
        for ppid in parent_pids:
            try:
                if psutil.pid_exists(ppid):
                    os.kill(ppid, signal.SIGCHLD)
                    logger.debug(f"向父进程 {ppid} 发送SIGCHLD信号")
            except (OSError, PermissionError):
                continue

        # 等待一小段时间让父进程处理
        import time
        time.sleep(0.1)

        # 重新检查僵尸进程是否被清理
        remaining_zombies = []
        for proc in psutil.process_iter(['pid', 'name', 'status']):
            try:
                proc_info = proc.info
                if proc_info['status'] == psutil.STATUS_ZOMBIE:
                    remaining_zombies.append(proc_info['pid'])
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue

        cleaned_count = len(zombie_pids) - len(remaining_zombies)

        if cleaned_count > 0:
            logger.info(f"成功清理了 {cleaned_count} 个僵尸进程")

        if remaining_zombies:
            logger.warning(f"仍有 {len(remaining_zombies)} 个僵尸进程未被清理: {remaining_zombies}")
            # 记录详细信息用于调试
            for zombie in zombie_pids:
                if zombie['pid'] in remaining_zombies:
                    logger.debug(f"未清理的僵尸进程: PID={zombie['pid']}, Name={zombie['name']}, PPID={zombie['ppid']}")

    except ImportError:
        logger.debug("psutil未安装，跳过僵尸进程清理")
    except Exception as e:
        logger.error(f"清理僵尸进程时出错: {e}")

    return cleaned_count

def _cleanup_chrome_related_zombies():
    """专门清理Chrome相关的僵尸进程"""
    cleaned_count = 0

    try:
        import psutil
        import os
        import signal

        chrome_zombies = []

        # 查找Chrome相关的僵尸进程
        for proc in psutil.process_iter(['pid', 'name', 'status', 'ppid']):
            try:
                proc_info = proc.info
                if (proc_info['status'] == psutil.STATUS_ZOMBIE and
                    proc_info.get('name', '').lower().find('chrome') != -1):
                    chrome_zombies.append(proc_info)
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue

        if not chrome_zombies:
            return 0

        logger.info(f"发现 {len(chrome_zombies)} 个Chrome相关僵尸进程")

        # 对于Chrome僵尸进程，尝试找到并清理其父进程
        for zombie in chrome_zombies:
            try:
                ppid = zombie.get('ppid', 0)
                if ppid > 1 and psutil.pid_exists(ppid):
                    parent = psutil.Process(ppid)
                    parent_name = parent.name().lower()

                    # 如果父进程也是Chrome相关进程，可能需要终止父进程
                    if 'chrome' in parent_name or 'playwright' in parent_name:
                        logger.info(f"终止Chrome父进程以清理僵尸子进程: PID={ppid}, Name={parent.name()}")
                        parent.terminate()
                        try:
                            parent.wait(timeout=3)
                        except psutil.TimeoutExpired:
                            parent.kill()
                        cleaned_count += 1
                    else:
                        # 向父进程发送信号
                        os.kill(ppid, signal.SIGCHLD)

            except (psutil.NoSuchProcess, psutil.AccessDenied, OSError):
                continue

    except ImportError:
        logger.debug("psutil未安装，跳过Chrome僵尸进程清理")
    except Exception as e:
        logger.error(f"清理Chrome僵尸进程时出错: {e}")

    return cleaned_count


def _emergency_chrome_cleanup():
    """紧急Chrome进程清理"""
    cleaned_count = 0
    try:
        import subprocess
        import psutil

        # 使用系统命令强制清理Chrome进程
        try:
            result = subprocess.run(['pkill', '-9', '-f', 'chrome'],
                                  capture_output=True, timeout=10)
            logger.info(f"pkill chrome命令执行完成，返回码: {result.returncode}")
        except subprocess.TimeoutExpired:
            logger.warning("pkill chrome命令超时")
        except Exception as e:
            logger.warning(f"pkill chrome命令失败: {e}")

        # 使用psutil清理剩余的Chrome进程
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                proc_info = proc.info
                if proc_info['name'] and 'chrome' in proc_info['name'].lower():
                    logger.info(f"强制终止Chrome进程: PID={proc_info['pid']}, Name={proc_info['name']}")
                    proc.kill()
                    cleaned_count += 1
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue
            except Exception as e:
                logger.debug(f"清理Chrome进程时出错: {e}")

    except ImportError:
        logger.warning("psutil未安装，无法执行紧急Chrome清理")
    except Exception as e:
        logger.error(f"紧急Chrome清理时出错: {e}")

    return cleaned_count


def _cleanup_playwright_temp_dirs():
    """清理Playwright临时目录"""
    cleaned_count = 0
    try:
        import os
        import shutil
        import glob

        # 清理/tmp下的playwright相关目录
        temp_patterns = [
            '/tmp/playwright_*',
            '/tmp/chromium*',
            '/tmp/.org.chromium.*',
            '/tmp/scoped_dir*'
        ]

        for pattern in temp_patterns:
            for path in glob.glob(pattern):
                try:
                    if os.path.isdir(path):
                        shutil.rmtree(path)
                        logger.debug(f"清理临时目录: {path}")
                        cleaned_count += 1
                    elif os.path.isfile(path):
                        os.remove(path)
                        logger.debug(f"清理临时文件: {path}")
                        cleaned_count += 1
                except Exception as e:
                    logger.debug(f"清理临时路径 {path} 时出错: {e}")

    except Exception as e:
        logger.error(f"清理Playwright临时目录时出错: {e}")

    return cleaned_count

    def _auto_cleanup_long_running_chrome(self):
        """自动清理长时间运行的Chrome进程"""
        cleaned_count = 0
        try:
            import psutil
            import time

            current_time = time.time()

            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time']):
                try:
                    proc_info = proc.info
                    if not proc_info['name']:
                        continue

                    # 检查是否是Chrome相关进程
                    if 'chrome' in proc_info['name'].lower():
                        create_time = proc_info.get('create_time', current_time)
                        runtime_minutes = (current_time - create_time) / 60

                        # 如果运行时间超过配置的最大时间，清理它
                        if runtime_minutes > self.max_chrome_runtime_minutes:
                            logger.info(f"自动清理长时间运行的Chrome进程: PID={proc_info['pid']}, "
                                      f"运行时间={runtime_minutes:.1f}分钟")
                            try:
                                proc = psutil.Process(proc_info['pid'])
                                proc.terminate()
                                proc.wait(timeout=3)
                                cleaned_count += 1
                            except psutil.TimeoutExpired:
                                proc.kill()
                                cleaned_count += 1
                            except (psutil.NoSuchProcess, psutil.AccessDenied):
                                continue

                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
                except Exception as e:
                    logger.debug(f"检查Chrome进程时出错: {e}")

        except ImportError:
            logger.debug("psutil未安装，跳过自动Chrome清理")
        except Exception as e:
            logger.error(f"自动清理Chrome进程时出错: {e}")

        if cleaned_count > 0:
            logger.info(f"自动清理了 {cleaned_count} 个长时间运行的Chrome进程")

        return cleaned_count

def _cleanup_all_chromium_processes():
    """清理所有Chromium进程，包括活跃进程"""
    cleaned_count = 0
    try:
        # 使用psutil获取更精确的进程信息
        try:
            import psutil
            current_pid = os.getpid()
            
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time']):
                try:
                    proc_info = proc.info
                    proc_name = proc_info['name'].lower()
                    
                    # 识别Chromium相关进程
                    if ('chrome' in proc_name or 'chromium' in proc_name) and proc_info['pid'] != current_pid:
                        cmdline = ' '.join(proc_info.get('cmdline', []))
                        
                        # 检查是否是Playwright启动的进程
                        if ('playwright' in cmdline.lower() or
                            '--remote-debugging-pipe' in cmdline or
                            '/ms-playwright/' in cmdline):

                            # 检查进程运行时间
                            create_time = proc_info.get('create_time', 0)
                            current_time = time.time()
                            runtime_minutes = (current_time - create_time) / 60

                            # 更智能的清理策略：考虑任务状态和运行时间
                            min_runtime = 20  # 最小运行时间保护
                            max_runtime = 60  # 最大运行时间

                            should_kill = False
                            if runtime_minutes < min_runtime:
                                # 运行时间太短，不清理
                                should_kill = False
                            elif runtime_minutes > max_runtime:
                                # 运行时间过长，必须清理
                                should_kill = True
                            elif _is_system_resource_critical():
                                # 资源紧急且运行时间超过最小保护时间
                                should_kill = True

                            if should_kill:
                                logger.info(f"清理Chromium进程 PID: {proc_info['pid']} (运行时间: {runtime_minutes:.1f}分钟)")
                                try:
                                    # 先尝试优雅终止
                                    proc.terminate()
                                    proc.wait(timeout=5)  # 给更多时间优雅退出
                                except psutil.TimeoutExpired:
                                    # 强制终止
                                    logger.warning(f"Chromium进程 {proc_info['pid']} 优雅退出失败，强制终止")
                                    proc.kill()
                                    proc.wait(timeout=2)

                                cleaned_count += 1
                                
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
                    
        except ImportError:
            # 回退到原有方法
            result = subprocess.run(['pgrep', '-f', 'chromium'], capture_output=True, text=True)
            if result.returncode == 0:
                pids = result.stdout.strip().split('\n')
                for pid_str in pids:
                    if pid_str.strip():
                        try:
                            pid = int(pid_str.strip())
                            os.kill(pid, signal.SIGTERM)
                            time.sleep(1)
                            try:
                                os.kill(pid, signal.SIGKILL)
                            except ProcessLookupError:
                                pass
                            logger.info(f"强制终止进程 PID: {pid}")
                            cleaned_count += 1
                        except (ValueError, ProcessLookupError, PermissionError):
                            pass
                            
    except Exception as e:
        logger.debug(f"清理Chromium进程时出错: {e}")
        
    return cleaned_count

def _is_system_resource_critical():
    """检查系统资源是否处于临界状态"""
    try:
        import psutil
        
        # 检查内存使用率
        memory_info = psutil.virtual_memory()
        if memory_info.percent > 85:
            return True
            
        # 检查当前进程的文件描述符数量
        current_process = psutil.Process()
        open_files_count = len(current_process.open_files())
        if open_files_count > 300:
            return True
            
        # 检查系统负载
        if hasattr(psutil, 'getloadavg'):
            load_avg = psutil.getloadavg()[0]
            cpu_count = psutil.cpu_count()
            if load_avg > cpu_count * 1.5:
                return True
                
    except ImportError:
        # 没有psutil时的简单检查
        try:
            result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
            if result.returncode == 0:
                chrome_count = result.stdout.lower().count('chrome')
                if chrome_count > 5:  # 超过5个Chrome进程认为是临界状态
                    return True
        except:
            pass
            
    return False

def _log_detailed_system_status():
    """记录详细的系统状态信息"""
    try:
        import psutil
        
        # 记录进程和线程信息
        current_process = psutil.Process()
        logger.info(f"当前进程线程数: {current_process.num_threads()}")
        
        # 记录系统范围的线程统计
        total_threads = 0
        chrome_processes = 0
        
        for proc in psutil.process_iter(['pid', 'name', 'num_threads']):
            try:
                proc_info = proc.info
                total_threads += proc_info.get('num_threads', 0)
                
                if 'chrome' in proc_info['name'].lower():
                    chrome_processes += 1
                    
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
                
        logger.info(f"系统总线程数: {total_threads}, Chrome进程数: {chrome_processes}")
        
        # 记录系统限制
        try:
            import resource
            soft_limit, hard_limit = resource.getrlimit(resource.RLIMIT_NPROC)
            logger.info(f"进程数限制: 软限制={soft_limit}, 硬限制={hard_limit}")
            
            soft_limit, hard_limit = resource.getrlimit(resource.RLIMIT_NOFILE)
            logger.info(f"文件描述符限制: 软限制={soft_limit}, 硬限制={hard_limit}")
        except ImportError:
            pass
            
    except ImportError:
        logger.debug("psutil不可用，跳过详细系统状态记录")

def _cleanup_temp_files():
    """内部函数：清理临时文件"""
    cleaned_count = 0
    temp_dirs = ['/tmp', '/var/tmp']
    
    # 如果是macOS，也检查用户临时目录
    if os.name == 'posix' and 'darwin' in os.uname().sysname.lower():
        temp_dirs.append(os.path.expanduser('~/tmp'))
        temp_dirs.append('/private/tmp')
    
    current_time = time.time()
    max_age_seconds = 3600  # 1小时以上的临时文件
    
    for temp_dir in temp_dirs:
        if not os.path.exists(temp_dir):
            continue
            
        try:
            for item in os.listdir(temp_dir):
                if 'playwright' in item.lower() or 'chromium' in item.lower():
                    item_path = os.path.join(temp_dir, item)
                    try:
                        mtime = os.path.getmtime(item_path)
                        if current_time - mtime > max_age_seconds:
                            if os.path.isdir(item_path):
                                import shutil
                                shutil.rmtree(item_path, ignore_errors=True)
                            else:
                                os.remove(item_path)
                            cleaned_count += 1
                    except (OSError, PermissionError):
                        pass
        except (OSError, PermissionError):
            pass
            
    return cleaned_count 