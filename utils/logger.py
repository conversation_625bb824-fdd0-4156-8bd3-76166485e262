import logging
import sys

def setup_logger(name='chatbot_automation_logger', level=logging.INFO):
    """配置并返回一个日志记录器。"""
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    handler = logging.StreamHandler(sys.stdout)
    handler.setFormatter(formatter)

    logger = logging.getLogger(name)
    logger.setLevel(level)
    if not logger.handlers: # 防止重复添加handler
        logger.addHandler(handler)
    
    return logger

# 创建一个默认的logger实例，方便其他模块直接导入使用
# 例如: from utils.logger import logger
# logger.info("This is an info message")
logger = setup_logger() 