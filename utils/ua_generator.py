from fake_useragent import UserAgent, FakeUserAgentError
from utils.logger import logger

# 默认情况下，fake-useragent 会尝试从网络获取最新的UA数据并缓存。
# 如果首次运行时没有网络，或者服务器不可达，可能会抛出 FakeUserAgentError。
# 可以配置 fallback UA 或使用本地数据源。

# 初始化 UserAgent 对象。可以全局初始化一个实例以利用其缓存。
# 为了避免在无法连接网络时初始化失败，可以提供一个备用UA。
_fallback_ua = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36"
try:
    ua_generator_instance = UserAgent(fallback=_fallback_ua)
except FakeUserAgentError:
    logger.warning("获取最新User-Agent列表失败。将使用备用或缓存的User-Agent。")
    # 如果初始化失败，尝试再次初始化，确保实例存在，即使它可能只依赖 fallback
    ua_generator_instance = UserAgent(fallback=_fallback_ua)

def get_random_user_agent(device_type: str = "pc") -> str:
    """
    生成一个随机的User-Agent字符串。

    :param device_type: 设备类型。支持 'pc' (或 'desktop'), 'mobile' (或 'smartphone'), 'tablet'.
                        也可以直接传递 'random' 获取完全随机的UA，或浏览器名如 'chrome', 'firefox'。
    :return: 一个随机的User-Agent字符串。
    """
    device_type_lower = device_type.lower()
    ua_string = _fallback_ua # 默认使用备用UA

    try:
        if device_type_lower in ["pc", "desktop"]:
            # 获取常见的桌面浏览器UA
            # .random 会尝试获取一个随机的UA，不保证特定是桌面但概率大
            # 可以指定浏览器如 ua_generator_instance.chrome, ua_generator_instance.firefox
            # 为了更明确是桌面，可以尝试几种桌面浏览器
            desktop_browsers = ['chrome', 'edge', 'firefox']
            import random
            selected_browser = random.choice(desktop_browsers)
            ua_string = getattr(ua_generator_instance, selected_browser, ua_generator_instance.random)
        elif device_type_lower in ["mobile", "smartphone"]:
            # fake-useragent 没有直接的 .mobile 或 .smartphone
            # 但通常 .android 或 .ios (如果可用) 会是移动UA
            # 或者我们可以依赖 .random，移动UA在列表中也占有一定比例
            # 一个简单的方式是获取Chrome或Safari的UA，它们同时有桌面和移动版，库会随机选择
            # 或者直接使用 .random 并相信其多样性
            # 为了倾向于移动，我们可以尝试获取 'android' 或 'iphone' (如果 UserAgent 类支持这些属性)
            # 或者更通用地，获取一个随机UA，然后检查其属性（但这增加了复杂性）
            # 简单起见，我们用 .random，或者可以指定一个通常在移动设备上出现的浏览器
            ua_string = ua_generator_instance.random # 或者 ua_generator_instance.chrome / .safari
        elif device_type_lower == "tablet":
            # 对于平板，.random 可能是最好的选择，因为它可能包含iPad等UA
            ua_string = ua_generator_instance.random # 或者 ua_generator_instance.safari
        elif hasattr(ua_generator_instance, device_type_lower):
            # 如果用户直接传递了浏览器名，如 'chrome', 'firefox'
            ua_string = getattr(ua_generator_instance, device_type_lower)
        else: # 包括 'random' 或无法识别的类型
            ua_string = ua_generator_instance.random
        
        logger.debug(f"为 {device_type} 生成的 UA: {ua_string}")
        return ua_string

    except FakeUserAgentError as e:
        logger.error(f"fake-useragent 错误: {e}。返回备用UA。")
        return _fallback_ua
    except Exception as e:
        logger.error(f"使用 fake-useragent 生成随机UA失败: {e}。返回备用UA。")
        return _fallback_ua


if __name__ == '__main__':
    logger.info("Testing User-Agent generation with fake-useragent...")
    
    logger.info(f"Default (random) UA: {get_random_user_agent('random')}")
    logger.info(f"PC (Chrome/Edge/Firefox) UA: {get_random_user_agent('pc')}")
    logger.info(f"Mobile UA: {get_random_user_agent('mobile')}")
    logger.info(f"Tablet UA: {get_random_user_agent('tablet')}")
    logger.info(f"Chrome UA: {get_random_user_agent('chrome')}")
    logger.info(f"Firefox UA: {get_random_user_agent('firefox')}")

    # 测试多次获取看是否变化
    for i in range(3):
        logger.info(f"Random UA {i+1}: {get_random_user_agent('random')}") 