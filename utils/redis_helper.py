# Placeholder for utils/redis_helper.py 

import redis
from utils.logger import logger
from datetime import date
from typing import Optional, List, Dict

class RedisConnectionError(Exception):
    """自定义Redis连接错误。"""
    pass

class RedisHelper:
    def __init__(self, host='localhost', port=6379, db=0, password=None):
        """
        初始化Redis连接。
        :param host: Redis服务器主机名。
        :param port: Redis服务器端口。
        :param db: Redis数据库编号。
        :param password: Redis密码 (如果有)。
        """
        self.host = host
        self.port = port
        self.db = db
        self.password = password
        self._client = None
        self._connect()

    def _connect(self):
        """建立到Redis的连接。"""
        try:
            # decode_responses=True 会让get等命令返回字符串而不是字节
            self._client = redis.Redis(
                host=self.host, 
                port=self.port, 
                db=self.db, 
                password=self.password,
                decode_responses=True,
                socket_connect_timeout=5, # 连接超时5秒
                socket_timeout=5 # 操作超时5秒
            )
            self._client.ping() # 验证连接
            logger.info(f"成功连接到 Redis {self.host}:{self.port}，数据库: {self.db}")
        except redis.exceptions.ConnectionError as e:
            logger.error(f"连接 Redis 失败: {e}")
            self._client = None # 确保客户端在连接失败时为None
            raise RedisConnectionError(f"无法连接到 Redis {self.host}:{self.port}: {e}") from e
        except Exception as e: # 其他可能的异常，例如认证失败
            logger.error(f"Redis 连接期间发生意外错误: {e}")
            self._client = None
            raise RedisConnectionError(f"Redis 连接意外错误: {e}") from e

    def get_client(self):
        """返回Redis客户端实例，如果连接丢失则尝试重新连接。"""
        if self._client is None:
            logger.warning("Redis 客户端为空，尝试重新连接...")
            self._connect() # 尝试重新连接
        
        # 即使重连尝试了，也可能再次失败，所以还是要检查
        if self._client is None:
            raise RedisConnectionError("尝试重连后 Redis 客户端仍不可用。")
            
        try:
            self._client.ping()
        except redis.exceptions.ConnectionError:
            logger.warning("Redis 连接丢失。尝试重新连接...")
            self._connect() # 再次尝试连接
            if self._client is None: # 如果重连后还是None
                 raise RedisConnectionError("PING 失败且重连尝试后 Redis 客户端仍不可用。")
        return self._client

    def get_daily_task_count_key(self, task_name_prefix="chat_task") -> str:
        """生成用于存储每日任务计数的Redis键。"""
        today_str = date.today().isoformat()
        return f"{task_name_prefix}_count:{today_str}"

    def increment_daily_task_count(self, task_name_prefix="chat_task") -> int:
        """
        原子性地增加指定任务的当日计数，并返回新的计数值。
        如果key不存在，会自动创建并设置为1。
        """
        client = self.get_client()
        key = self.get_daily_task_count_key(task_name_prefix)
        try:
            new_count = client.incr(key)
            logger.info(f"已增加每日任务计数器 '{key}'。新计数值: {new_count}")
            return new_count
        except redis.exceptions.RedisError as e:
            logger.error(f"Redis INCR 命令执行失败，键: '{key}'，错误: {e}")
            raise # 通常incr不太会失败除非连接问题，已由get_client处理

    def get_daily_task_count(self, task_name_prefix="chat_task") -> int:
        """
        获取指定任务的当日计数值。如果key不存在，返回0。
        """
        client = self.get_client()
        key = self.get_daily_task_count_key(task_name_prefix)
        try:
            count = client.get(key)
            if count is None:
                logger.info(f"每日任务计数键 '{key}' 不存在。返回 0。")
                return 0
            return int(count)
        except redis.exceptions.RedisError as e:
            logger.error(f"Redis GET 命令执行失败，键: '{key}'，错误: {e}")
            return 0 # 在获取失败时返回0，避免影响主逻辑
        except ValueError as e:
            logger.error(f"Redis 中键 '{key}' 的值不是有效整数: {count}。错误: {e}")
            return 0 # 值不是整数，当作0处理

    def reset_daily_task_count(self, task_name_prefix="chat_task"):
        """
        重置（删除）指定任务的当日计数键。
        主要用于测试或特殊维护场景。
        """
        client = self.get_client()
        key = self.get_daily_task_count_key(task_name_prefix)
        try:
            client.delete(key)
            logger.info(f"已重置 (删除) 每日任务计数键 '{key}'.")
        except redis.exceptions.RedisError as e:
            logger.error(f"Redis DELETE 命令执行失败，键: '{key}'，错误: {e}")
            raise

    def set_value(self, key: str, value: str, ex: Optional[int] = None):
        # ... existing code ...
        pass

    def get_value(self, key: str) -> Optional[str]:
        # ... existing code ...
        pass

    def get_hash_fields(self, hash_key: str, fields: List[str]) -> Dict[str, Optional[str]]:
        """
        从 Redis Hash 中获取多个字段的值。

        Args:
            hash_key: Hash 的键名。
            fields: 要获取的字段名列表。

        Returns:
            一个字典，键是字段名，值是字段的字符串值。如果字段不存在，则对应的值为 None。
            如果发生 Redis 连接错误，则抛出 RedisConnectionError。
        """
        if not self._client:
            raise RedisConnectionError("Redis client not initialized or connection failed.")
        try:
            logger.debug(f"从 Redis Hash '{hash_key}' 获取字段: {fields}")
            values = self._client.hmget(hash_key, fields)
            # hmget 返回一个与字段顺序对应的值列表，如果字段不存在则为 None
            # 当 decode_responses=True 时, value 已经是 str 类型了
            result = {field: value if value is not None else None 
                      for field, value in zip(fields, values)}
            logger.debug(f"从 Redis Hash '{hash_key}' 获取结果: {result}")
            return result
        except redis.exceptions.ConnectionError as e:
            logger.error(f"连接 Redis 时发生错误 (hmget on '{hash_key}'): {e}")
            raise RedisConnectionError(f"Failed to connect to Redis while getting hash fields: {e}")
        except Exception as e:
            logger.error(f"从 Redis Hash '{hash_key}' 获取字段时发生未知错误: {e}", exc_info=True)
            # 对于其他未知异常，也将其包装为 RedisConnectionError 或一个更通用的自定义异常
            raise RedisConnectionError(f"An unexpected error occurred while getting hash fields from '{hash_key}': {e}")

    def _get_daily_chat_state_key(self, date_obj: date, task_name_prefix: str) -> str:
        """生成用于存储每日聊天交互状态的Redis键。"""
        return f"{task_name_prefix}:{date_obj.isoformat()}:chat_interaction_state"

    def set_daily_chat_interaction_state(self, date_obj: date, task_name_prefix: str, chat_task_count: int, max_daily_tasks_basis: int):
        """
        将指定日期的聊天任务数和其计算基准（MAX_DAILY_TASKS）存入 Redis Hash。
        键会在2天后自动过期。
        """
        client = self.get_client()
        key = self._get_daily_chat_state_key(date_obj, task_name_prefix)
        try:
            mapping = {
                "chat_task_count": chat_task_count,
                "max_daily_tasks_basis": max_daily_tasks_basis
            }
            client.hmset(key, mapping)
            client.expire(key, 172800)  # 172800 秒 = 2 天
            logger.info(f"已设置每日聊天交互状态键 '{key}' 为: {mapping}，2天后过期。")
        except redis.exceptions.RedisError as e:
            logger.error(f"Redis HMSET/EXPIRE 命令执行失败，键: '{key}'，错误: {e}")
            # 根据需要，可以决定是否向上抛出异常

    def get_daily_chat_interaction_state(self, date_obj: date, task_name_prefix: str) -> Optional[tuple[int, int]]:
        """
        获取指定日期的聊天任务数和其计算基准（MAX_DAILY_TASKS）。
        如果键不存在、字段不完整或值无法转换为整数，则返回 None。
        """
        client = self.get_client()
        key = self._get_daily_chat_state_key(date_obj, task_name_prefix)
        try:
            state_data = client.hgetall(key)
            if not state_data: # Key不存在或为空Hash
                logger.info(f"每日聊天交互状态键 '{key}' 未找到或为空。")
                return None

            chat_task_count_str = state_data.get("chat_task_count")
            max_daily_tasks_basis_str = state_data.get("max_daily_tasks_basis")

            if chat_task_count_str is None or max_daily_tasks_basis_str is None:
                logger.warning(f"每日聊天交互状态键 '{key}' 数据不完整: {state_data}。")
                return None
            
            try:
                chat_task_count = int(chat_task_count_str)
                max_daily_tasks_basis = int(max_daily_tasks_basis_str)
                logger.info(f"从Redis获取到每日聊天交互状态键 '{key}': chat_count={chat_task_count}, basis={max_daily_tasks_basis}")
                return chat_task_count, max_daily_tasks_basis
            except ValueError:
                logger.error(f"无法将每日聊天交互状态键 '{key}' 中的值转换为整数: {state_data}。")
                return None
        except redis.exceptions.RedisError as e:
            logger.error(f"Redis HGETALL 命令执行失败，键: '{key}'，错误: {e}")
            return None # 获取失败，返回None

    def clear_daily_task_count(self, task_name_prefix: str = "task"):
        # ... existing code ...
        pass

# 示例用法
if __name__ == '__main__':
    logger.info("Testing RedisHelper...")
    try:
        # 假设Redis在本地默认端口运行且无密码
        redis_helper = RedisHelper(host='localhost', port=6379, db=0)
        
        # 测试前先重置计数，确保测试环境干净
        redis_helper.reset_daily_task_count("my_test_task")

        count1 = redis_helper.get_daily_task_count("my_test_task")
        logger.info(f"Initial count for my_test_task: {count1}") # 应为 0

        new_count1 = redis_helper.increment_daily_task_count("my_test_task")
        logger.info(f"Count after 1st increment: {new_count1}") # 应为 1

        new_count2 = redis_helper.increment_daily_task_count("my_test_task")
        logger.info(f"Count after 2nd increment: {new_count2}") # 应为 2

        current_count = redis_helper.get_daily_task_count("my_test_task")
        logger.info(f"Current count from get: {current_count}") # 应为 2

        # 测试另一个任务的计数器
        count_other = redis_helper.get_daily_task_count("another_task")
        logger.info(f"Initial count for another_task: {count_other}") # 应为 0
        new_count_other = redis_helper.increment_daily_task_count("another_task")
        logger.info(f"Count for another_task after increment: {new_count_other}") # 应为 1

        logger.info("RedisHelper test completed successfully.")

    except RedisConnectionError as e:
        logger.error(f"Redis connection failed during test: {e}. Ensure Redis is running.")
    except Exception as e:
        logger.error(f"An unexpected error occurred during RedisHelper test: {e}") 