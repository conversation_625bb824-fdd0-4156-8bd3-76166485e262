# chatbot_automation/config.yaml

# ------------------------------------------------------------------------------
# 基本配置 (Essential URLs and Keys)
# ------------------------------------------------------------------------------

DYNAMIC_URL_API_ENDPOINT: "http://www.zhikaobibei.com/api3/get-article-url" # 获取目标URL的API的URL 
DYNAMIC_URL_API_KEY: "zk789012" # 获取目标URL的API的密钥
DYNAMIC_URL_PROJECT_NAMES: [
  "研究生", "一消", "人力资源", "物业经理", "法考", "教资", "语培", "二消", 
  "安全工程师", "中会", "注会", "药师", "执业医师", "护士", "银行从业", 
  "留学", "小学", "初中", "高中", "证券从业", "本科", "基金从业", "经济师", 
  "一建", "二建", "一造", "监理", "二造", "建筑九大员", "造价实操", "公务员", 
  "社工", "专科", "初会", "消操", "健康管理师", "心理咨询师", "公共营养师", 
  "无人机", "月嫂", "电工"
]
PROXY_API_URL: "https://www.lthttp.com/iplist" # 您的代理IP获取API的URL
PROXY_API_KEY: "4191e4d2d5698a59"      # 您的代理API密钥
OPENAI_API_KEY: "78795ec7-d38d-4adb-a9eb-46aa6677b568"   # 您的OpenAI API密钥

# ------------------------------------------------------------------------------
# 代理IP API 特定参数 (Proxy API Specific Parameters)
# ------------------------------------------------------------------------------
# 这是一个可选的字典，包含传递给代理IP API的额外参数 (除了key和count)
# 具体参数请参考您的代理服务商API文档
PROXY_API_PARAMS:
  protocol: 0   # 示例: 0 表示 HTTP/HTTPS
  type: 0       # 示例: 数据格式(0 JSON、1 TXT)
  isp: 0      # 示例: 运营商(0 随机、 1 电信、 2 联通、 3 移动)
  distinct: 1 # 示例: IP去重(1 去重、 0 不去重)
  os: 1      # 示例: 返回出口IP
  cs: 1      # 示例: 返回城市
  is: 0      # 示例: 返回运营商
  es: 0      # 示例: 返回过期时间
  textSep: 1  # 示例: 
  province: ''  # 示例: 省
  city: ''  # 示例: 市
  # ... 其他您的代理API支持的参数

# ------------------------------------------------------------------------------
# OpenAI 配置 (OpenAI Configuration)
# ------------------------------------------------------------------------------

OPENAI_MODEL: "doubao-1-5-pro-32k-250115" # 要使用的OpenAI模型
OPENAI_BASE_URL: "https://ark.cn-beijing.volces.com/api/v3" # 可选：指定OpenAI API的基础URL，例如用于兼容其他服务或代理。如果为null或空，则使用openai库默认URL。

# ------------------------------------------------------------------------------
# Redis 配置 (Redis Configuration)
# ------------------------------------------------------------------------------
REDIS_HOST: "*************"
REDIS_PORT: 63791
REDIS_DB: 0
REDIS_PASSWORD: "auto_form_redis" # 如果您的Redis有密码，请填写字符串，否则为 null 或留空
REDIS_TASK_NAME_PREFIX: "chat_task" # Redis中存储每日任务计数器的键的前缀

# ------------------------------------------------------------------------------
# 任务调度与控制 (Task Scheduling and Control - main.py)
# ------------------------------------------------------------------------------
MAX_DAILY_TASKS: 1000              # 每日执行的最大任务数
START_HOUR: 4                     # 任务执行开始小时 (24小时制, 0-23), 例如 5 代表早上5点
END_HOUR: 0                      # 任务执行结束小时 (24小时制, 0-23), 设置为0表示执行到当天的23:59:59
ESTIMATED_SINGLE_TASK_DURATION_SECONDS: 100 # 预估的单次任务平均耗时（秒），用于计算线程数
MIN_SCHEDULER_SLEEP_SECONDS: 5   # 当无任务或不在执行时间段时，调度器的最小休眠时间（秒）
TASK_INTERVAL_RANDOM_FACTOR_MIN: 0.3 # 任务提交随机间隔的最小因子 (乘以平均间隔)
TASK_INTERVAL_RANDOM_FACTOR_MAX: 0.7 # 任务提交随机间隔的最大因子 (乘以平均间隔)

# ------------------------------------------------------------------------------
# 线程配置 (Threading Configuration - main.py)
# ------------------------------------------------------------------------------
MIN_THREADS: 1
MAX_THREADS: 3 # 大幅降低最大线程数，减少资源消耗

# ------------------------------------------------------------------------------
# 任务执行器具体配置 (Task Runner Specifics - task_runner.py)
# ------------------------------------------------------------------------------
USER_AGENT_TYPE: "pc" # User-Agent类型: 'pc', 'mobile', 'tablet', 'random', 或具体浏览器名如 'chrome', 'firefox'
HEADLESS_BROWSER: true # 是否以无头模式运行浏览器: true (无界面) 或 false (有界面, 便于调试)
MAX_CHAT_INTERACTIONS: 3      # 每个任务与客服的最大交互次数 (OpenAI调用次数)
MIN_CUSTOMER_MSG_LEN_FOR_OPENAI: 10 # 客服消息的最小长度（字符数）才调用OpenAI，避免对简单回复调用
MAX_TASK_RUN_DURATION_SECONDS: 450 # 单个任务最大允许执行时长（秒），增加到450秒以适应优化后的性能

# ------------------------------------------------------------------------------
# 聊天交互控制 (Chat Interaction Control - main.py & task_runner.py)
# ------------------------------------------------------------------------------
CHAT_INTERACTION_PERCENTAGE_MIN: 0.3  # 执行聊天交互的任务的最小百分比 (例如 0.2 表示 20%)
CHAT_INTERACTION_PERCENTAGE_MAX: 0.4  # 执行聊天交互的任务的最大百分比 (例如 0.3 表示 30%)
POST_CLICK_WAIT_SECONDS_MIN: 3        # 点击客服按钮后，主要任务完成前的最小等待时间（秒）
POST_CLICK_WAIT_SECONDS_MAX: 8        # 点击客服按钮后，主要任务完成前的最大等待时间（秒）

# ------------------------------------------------------------------------------
# Playwright 元素选择器 (CSS Selectors - 请确保这些与您的目标网站结构匹配)
# ------------------------------------------------------------------------------
CUSTOMER_SERVICE_BUTTON_SELECTOR: "#ai-chat-button"  # 主页面上打开客服聊天窗口的按钮
IFRAME_SELECTOR: "#edu-bot-iframe2"                 # 客服聊天界面的iframe选择器
CHAT_INPUT_SELECTOR: "textarea.uni-textarea-textarea" # iframe内部的聊天输入框
CHAT_MESSAGE_AREA_SELECTOR: "uni-scroll-view.message-container.chat-box div.uni-scroll-view-content" # iframe内部，包含所有聊天消息的容器
CUSTOMER_MESSAGE_SELECTOR: "uni-view.message-left uni-view.message-common.message-content" # iframe内部，单条客服消息的内容元素

# ------------------------------------------------------------------------------
# 任务内部超时与延迟 (Timeouts and Delays within Task - task_runner.py)
# ------------------------------------------------------------------------------
INITIAL_MESSAGE_WAIT_SECONDS: 5    # 打开聊天后，等待客服第一条消息的最大时间（秒）
CUSTOMER_REPLY_WAIT_SECONDS: 5     # 聊天循环中，等待客服回复的最大时间（秒）
MIN_INTERACTION_DELAY_SECONDS: 3    # 我方机器人发送消息后，到下一次动作（如检查回复）的最小延迟（秒）
MAX_INTERACTION_DELAY_SECONDS: 5    # 我方机器人发送消息后，到下一次动作（如检查回复）的最大延迟（秒）
END_TASK_WAIT_SECONDS_MIN: 1        # 任务成功完成后，清理资源前的最小随机等待时间（秒）
END_TASK_WAIT_SECONDS_MAX: 5       # 任务成功完成后，清理资源前的最大随机等待时间（秒）

# ------------------------------------------------------------------------------
# 重试机制 (Retry Mechanism - 用于 @retry 装饰器)
# ------------------------------------------------------------------------------
MAX_RETRIES: 2          # 对于被 @retry 装饰的函数（如API调用、Playwright操作）的最大重试次数
RETRY_DELAY_SECONDS: 5 # 每次重试之间的延迟时间（秒） 


# ------------------------------------------------------------------------------
# OpenAI 系统消息 (OpenAI System Message - task_runner.py)
# ------------------------------------------------------------------------------
OPENAI_SYSTEM_MESSAGE: |
  你现在是一个对当前客服发过来的话题感兴趣的普通人，正在与在线客服交谈。你需要以自然、真实的方式进行对话。

  背景设定：
  1. 你是从搜索引擎看到这个话题后产生了兴趣
  2. 你对这个话题了解不多，但有学习意愿
  3. 你希望获取更多资料和信息
  4. 你的态度友好但不过分热情

  行为准则：
  1. 保持简短的回复，每次回复的句子不要超过15个字
  2. 使用日常口语化表达，少用标点符号
  3. 提问时保持简单直接
  4. 表现出一般人的决策过程
  5. 表达疑惑时语句可以不完整

  重要禁止事项：
  1. 不要暴露AI身份
  2. 绝对不要提供任何电话、邮箱或其他联系方式
  3. 不要使用过于书面的语言
  4. 不要一次问太多问题
  5. 不要重复问题
  6. 不要表现出专业知识
  7. 不要表现得像客服一样
  8. 不要回复带有不留联系方式意图的内容

  语言风格：
  1. 偶尔使用"..."表示思考
  2. 口语化，少用标点符号
  3. 不完整句子是可以的
  4. 简短随意，像普通人聊天 

# ------------------------------------------------------------------------------
# 系统资源管理 (System Resource Management) - 优化的资源控制配置
# ------------------------------------------------------------------------------
SYSTEM_CLEANUP_INTERVAL_MINUTES: 15  # 系统清理间隔（分钟）- 减少清理频率
MAX_TEMP_FILES_AGE_HOURS: 2          # 临时文件最大保留时间（小时）- 适中清理
ENABLE_SYSTEM_CLEANUP: true          # 是否启用系统清理功能
BROWSER_RESTART_INTERVAL_TASKS: 10   # 每执行多少个任务后重启浏览器实例（减少重启频率）

# 新增：智能资源管理参数 - 平衡的资源控制阈值
EMERGENCY_CLEANUP_ENABLED: true      # 启用紧急清理模式
MAX_CHROME_PROCESSES: 3              # 系统中允许的最大Chrome进程数（适中限制）
MAX_SYSTEM_MEMORY_PERCENT: 85        # 系统内存使用率超过此值时触发紧急清理（适中限制）
MAX_OPEN_FILES_PER_PROCESS: 400      # 单个进程最大文件描述符数（适中限制）
THREAD_MONITORING_ENABLED: true      # 启用线程监控

# 智能清理增强配置
AUTO_CLEANUP_ENABLED: true           # 启用自动清理功能
AUTO_CLEANUP_INTERVAL_MINUTES: 15    # 自动清理检查间隔（分钟）
MAX_CHROME_RUNTIME_MINUTES: 45       # Chrome进程最大运行时间（分钟），超过自动清理
MIN_CHROME_RUNTIME_MINUTES: 20       # Chrome进程最小运行时间保护（分钟）
TASK_AWARE_CLEANUP: true             # 启用任务感知清理
ACTIVE_TASK_PROTECTION: true         # 启用活跃任务保护

# 自动健康监控配置
AUTO_HEALTH_MONITORING_ENABLED: true # 启用自动健康监控
HEALTH_CHECK_INTERVAL_MINUTES: 5     # 健康检查间隔（分钟）
AUTO_HEALING_ENABLED: true           # 启用自动自愈功能
EMERGENCY_CLEANUP_THRESHOLD: 3       # 连续异常次数触发紧急清理

# ------------------------------------------------------------------------------
# Playwright 浏览器配置 (Browser Configuration - task_runner.py)
# ------------------------------------------------------------------------------

 