#!/usr/bin/env python3
"""
资源耗尽问题修复脚本
解决Chrome进程泄漏、文件描述符耗尽等问题
"""

import os
import sys
import subprocess
import time
import psutil
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_system_resources():
    """检查系统资源状态"""
    logger.info("检查系统资源状态...")
    
    # 检查Chrome进程数量
    chrome_count = 0
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            proc_info = proc.info
            if proc_info['name'] and 'chrome' in proc_info['name'].lower():
                chrome_count += 1
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    logger.info(f"当前Chrome进程数量: {chrome_count}")
    
    # 检查内存使用率
    memory = psutil.virtual_memory()
    logger.info(f"内存使用率: {memory.percent}% (可用: {memory.available // 1024 // 1024}MB)")
    
    # 检查文件描述符
    try:
        current_process = psutil.Process()
        open_files = current_process.open_files()
        logger.info(f"当前进程打开文件数量: {len(open_files)}")
    except Exception as e:
        logger.warning(f"无法检查文件描述符: {e}")
    
    # 检查系统线程数
    try:
        total_threads = 0
        for proc in psutil.process_iter(['num_threads']):
            try:
                total_threads += proc.info['num_threads']
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        logger.info(f"系统总线程数: {total_threads}")
    except Exception as e:
        logger.warning(f"无法检查系统线程数: {e}")

def kill_all_chrome_processes():
    """强制终止所有Chrome进程"""
    logger.info("强制终止所有Chrome进程...")
    killed_count = 0
    
    # 使用系统命令
    try:
        result = subprocess.run(['pkill', '-9', '-f', 'chrome'], 
                              capture_output=True, timeout=10)
        logger.info(f"pkill chrome命令执行完成，返回码: {result.returncode}")
    except subprocess.TimeoutExpired:
        logger.warning("pkill chrome命令超时")
    except Exception as e:
        logger.warning(f"pkill chrome命令失败: {e}")
    
    # 使用psutil清理剩余进程
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            proc_info = proc.info
            if proc_info['name'] and 'chrome' in proc_info['name'].lower():
                logger.info(f"终止Chrome进程: PID={proc_info['pid']}, Name={proc_info['name']}")
                proc.kill()
                killed_count += 1
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            continue
        except Exception as e:
            logger.debug(f"终止Chrome进程时出错: {e}")
    
    logger.info(f"已终止 {killed_count} 个Chrome进程")
    return killed_count

def cleanup_temp_files():
    """清理临时文件"""
    logger.info("清理临时文件...")
    cleaned_count = 0
    
    import glob
    import shutil
    
    # 清理模式
    temp_patterns = [
        '/tmp/playwright_*',
        '/tmp/chromium*',
        '/tmp/.org.chromium.*',
        '/tmp/scoped_dir*'
    ]
    
    for pattern in temp_patterns:
        for path in glob.glob(pattern):
            try:
                if os.path.isdir(path):
                    shutil.rmtree(path)
                    logger.debug(f"清理临时目录: {path}")
                    cleaned_count += 1
                elif os.path.isfile(path):
                    os.remove(path)
                    logger.debug(f"清理临时文件: {path}")
                    cleaned_count += 1
            except Exception as e:
                logger.debug(f"清理临时路径 {path} 时出错: {e}")
    
    logger.info(f"已清理 {cleaned_count} 个临时文件/目录")
    return cleaned_count

def set_system_limits():
    """设置系统资源限制"""
    logger.info("设置系统资源限制...")
    
    try:
        import resource
        
        # 设置文件描述符限制
        soft, hard = resource.getrlimit(resource.RLIMIT_NOFILE)
        logger.info(f"当前文件描述符限制: 软限制={soft}, 硬限制={hard}")
        
        # 尝试提高软限制
        new_soft = min(65536, hard)
        resource.setrlimit(resource.RLIMIT_NOFILE, (new_soft, hard))
        logger.info(f"已设置文件描述符软限制为: {new_soft}")
        
        # 设置进程数限制
        try:
            soft, hard = resource.getrlimit(resource.RLIMIT_NPROC)
            logger.info(f"当前进程数限制: 软限制={soft}, 硬限制={hard}")
            
            new_soft = min(32768, hard) if hard != -1 else 32768
            resource.setrlimit(resource.RLIMIT_NPROC, (new_soft, hard))
            logger.info(f"已设置进程数软限制为: {new_soft}")
        except Exception as e:
            logger.warning(f"设置进程数限制失败: {e}")
            
    except ImportError:
        logger.warning("resource模块不可用，无法设置系统限制")
    except Exception as e:
        logger.error(f"设置系统限制时出错: {e}")

def main():
    """主函数"""
    logger.info("开始资源耗尽问题修复...")
    
    # 检查当前状态
    check_system_resources()
    
    # 设置系统限制
    set_system_limits()
    
    # 清理Chrome进程
    kill_all_chrome_processes()
    
    # 等待进程完全终止
    time.sleep(3)
    
    # 清理临时文件
    cleanup_temp_files()
    
    # 强制垃圾收集
    import gc
    gc.collect()
    
    # 再次检查状态
    logger.info("修复完成，再次检查系统资源状态...")
    check_system_resources()
    
    logger.info("资源耗尽问题修复完成！")

if __name__ == "__main__":
    main()
