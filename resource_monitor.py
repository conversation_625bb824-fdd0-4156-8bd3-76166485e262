#!/usr/bin/env python3
"""
实时资源监控脚本
监控Chrome进程数量、内存使用率、文件描述符等
"""

import time
import psutil
import logging
import signal
import sys
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ResourceMonitor:
    def __init__(self):
        self.running = True
        self.max_chrome_processes = 5
        self.max_memory_percent = 80
        self.max_open_files = 500
        self.check_interval = 30  # 30秒检查一次
        
    def signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info("收到停止信号，正在退出...")
        self.running = False
        
    def count_chrome_processes(self):
        """统计Chrome进程数量"""
        chrome_count = 0
        chrome_processes = []
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time']):
            try:
                proc_info = proc.info
                if proc_info['name'] and 'chrome' in proc_info['name'].lower():
                    chrome_count += 1
                    runtime = time.time() - proc_info['create_time']
                    chrome_processes.append({
                        'pid': proc_info['pid'],
                        'name': proc_info['name'],
                        'runtime_minutes': runtime / 60
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
                
        return chrome_count, chrome_processes
    
    def get_memory_usage(self):
        """获取内存使用情况"""
        memory = psutil.virtual_memory()
        return {
            'percent': memory.percent,
            'available_mb': memory.available // 1024 // 1024,
            'used_mb': memory.used // 1024 // 1024,
            'total_mb': memory.total // 1024 // 1024
        }
    
    def get_open_files_count(self):
        """获取当前进程打开的文件数量"""
        try:
            current_process = psutil.Process()
            return len(current_process.open_files())
        except Exception:
            return 0
    
    def get_system_threads(self):
        """获取系统总线程数"""
        total_threads = 0
        try:
            for proc in psutil.process_iter(['num_threads']):
                try:
                    total_threads += proc.info['num_threads']
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception:
            pass
        return total_threads
    
    def cleanup_old_chrome_processes(self, chrome_processes):
        """清理运行时间过长的Chrome进程"""
        cleaned_count = 0
        
        for proc_info in chrome_processes:
            if proc_info['runtime_minutes'] > 15:  # 运行超过15分钟
                try:
                    proc = psutil.Process(proc_info['pid'])
                    logger.warning(f"终止长时间运行的Chrome进程: PID={proc_info['pid']}, 运行时间={proc_info['runtime_minutes']:.1f}分钟")
                    proc.terminate()
                    try:
                        proc.wait(timeout=3)
                    except psutil.TimeoutExpired:
                        proc.kill()
                    cleaned_count += 1
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                except Exception as e:
                    logger.error(f"终止Chrome进程时出错: {e}")
        
        return cleaned_count
    
    def emergency_cleanup(self):
        """紧急清理"""
        logger.warning("执行紧急资源清理...")
        
        # 强制清理所有Chrome进程
        import subprocess
        try:
            subprocess.run(['pkill', '-9', '-f', 'chrome'], 
                         capture_output=True, timeout=10)
            logger.info("已执行强制Chrome进程清理")
        except Exception as e:
            logger.error(f"强制清理Chrome进程失败: {e}")
        
        # 清理临时文件
        try:
            import glob
            import shutil
            import os
            
            temp_patterns = [
                '/tmp/playwright_*',
                '/tmp/chromium*',
                '/tmp/.org.chromium.*'
            ]
            
            for pattern in temp_patterns:
                for path in glob.glob(pattern):
                    try:
                        if os.path.isdir(path):
                            shutil.rmtree(path)
                        elif os.path.isfile(path):
                            os.remove(path)
                    except Exception:
                        pass
                        
            logger.info("已清理临时文件")
        except Exception as e:
            logger.error(f"清理临时文件失败: {e}")
    
    def check_resources(self):
        """检查系统资源"""
        # 检查Chrome进程
        chrome_count, chrome_processes = self.count_chrome_processes()
        
        # 检查内存
        memory_info = self.get_memory_usage()
        
        # 检查文件描述符
        open_files = self.get_open_files_count()
        
        # 检查系统线程
        total_threads = self.get_system_threads()
        
        # 记录状态
        logger.info(f"资源状态 - Chrome进程: {chrome_count}, 内存: {memory_info['percent']:.1f}%, "
                   f"打开文件: {open_files}, 系统线程: {total_threads}")
        
        # 检查是否需要清理
        issues = []
        
        if chrome_count > self.max_chrome_processes:
            issues.append(f"Chrome进程过多({chrome_count}>{self.max_chrome_processes})")
            
        if memory_info['percent'] > self.max_memory_percent:
            issues.append(f"内存使用率过高({memory_info['percent']:.1f}%>{self.max_memory_percent}%)")
            
        if open_files > self.max_open_files:
            issues.append(f"打开文件过多({open_files}>{self.max_open_files})")
        
        if issues:
            logger.warning(f"检测到资源问题: {'; '.join(issues)}")
            
            # 先尝试清理长时间运行的Chrome进程
            cleaned = self.cleanup_old_chrome_processes(chrome_processes)
            if cleaned > 0:
                logger.info(f"已清理 {cleaned} 个长时间运行的Chrome进程")
            
            # 如果问题严重，执行紧急清理
            if chrome_count > self.max_chrome_processes * 2 or memory_info['percent'] > 90:
                self.emergency_cleanup()
        
        return len(issues) == 0
    
    def run(self):
        """运行监控"""
        logger.info("开始资源监控...")
        logger.info(f"监控阈值 - Chrome进程: {self.max_chrome_processes}, "
                   f"内存: {self.max_memory_percent}%, 文件: {self.max_open_files}")
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        while self.running:
            try:
                self.check_resources()
                
                # 等待下次检查
                for _ in range(self.check_interval):
                    if not self.running:
                        break
                    time.sleep(1)
                    
            except KeyboardInterrupt:
                logger.info("收到键盘中断，正在退出...")
                break
            except Exception as e:
                logger.error(f"监控过程中发生错误: {e}")
                time.sleep(5)
        
        logger.info("资源监控已停止")

def main():
    """主函数"""
    monitor = ResourceMonitor()
    monitor.run()

if __name__ == "__main__":
    main()
