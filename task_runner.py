# Placeholder for task_runner.py 
import asyncio
import random
import time
from playwright.async_api import async_playwright, Play<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>L<PERSON>ator, TimeoutError as PlaywrightTimeoutError, BrowserContext
from typing import Optional, List, Dict, Any

from utils.logger import logger
from utils.decorators import retry, retry_async
from api_clients.proxy_client import get_proxy_ip, ProxyAPIError, NoAvailableProxyError
from api_clients.openai_client import get_openai_response, OpenAIError
from api_clients.dynamic_url_client import get_dynamic_target_url, DynamicURLError, NoProjectNamesError
from utils.ua_generator import get_random_user_agent
# from utils.redis_helper import RedisHelper # RedisHelper将在main.py中实例化并传递或通过回调更新

class TaskRunError(Exception):
    """自定义任务执行期间的错误。"""
    pass

class TaskRunner:
    def __init__(self, playwright: Playwright, config: dict, task_id: int, should_perform_chat_interaction: bool):
        """
        初始化任务执行器。
        :param playwright: An already started Playwright instance.
        :param config: Dictionary containing all configurations.
        :param task_id: Unique ID for the current task, for logging.
        :param should_perform_chat_interaction: Boolean indicating if chat interaction should be performed.
        """
        self.playwright: Playwright = playwright
        self.config = config
        self.task_id = task_id
        self.should_perform_chat_interaction = False  # Temporarily disable chat
        self.browser: Optional[Browser] = None
        self.page: Optional[Page] = None
        self._context: Optional[BrowserContext] = None
        self.chat_iframe: Optional[FrameLocator] = None
        self.last_customer_message_count = 0 # 用于辅助判断是否有新消息
        self.chat_history: List[Dict[str, Any]] = []

        # 从配置中提取常用项
        # self.target_url = self.config.get('TARGET_URL') # Removed: URL will be fetched dynamically
        
        # Dynamic URL fetching config
        self.dynamic_url_api_endpoint = self.config.get('DYNAMIC_URL_API_ENDPOINT')
        self.dynamic_url_api_key = self.config.get('DYNAMIC_URL_API_KEY')
        self.dynamic_url_project_names = self.config.get('DYNAMIC_URL_PROJECT_NAMES', []) # Default to empty list

        # 随机化本次任务的最大交互次数
        max_interactions_config = self.config.get('MAX_CHAT_INTERACTIONS', 3)
        if max_interactions_config < 1:
            logger.warning(f"[任务 {self.task_id}] MAX_CHAT_INTERACTIONS 配置为 {max_interactions_config}，至少应为1。将使用1作为最大交互次数。")
            max_interactions_config = 1
        self.current_max_interactions = random.randint(1, max_interactions_config)
        logger.info(f"[任务 {self.task_id}] 本次任务最大交互次数随机设定为: {self.current_max_interactions} (配置上限: {max_interactions_config})")

        self.openai_api_key = self.config.get('OPENAI_API_KEY')
        self.openai_model = self.config.get('OPENAI_MODEL', 'gpt-3.5-turbo')
        self.openai_base_url = self.config.get('OPENAI_BASE_URL', None)
        self.min_customer_message_length_for_openai = self.config.get('MIN_CUSTOMER_MSG_LEN_FOR_OPENAI', 10)
        
        # 新增：读取点击后等待的配置
        self.post_click_wait_min_seconds = self.config.get('POST_CLICK_WAIT_SECONDS_MIN', 3)
        self.post_click_wait_max_seconds = self.config.get('POST_CLICK_WAIT_SECONDS_MAX', 5)
        
        # 新增：单个任务最大执行时长
        self.max_task_run_duration_seconds = self.config.get('MAX_TASK_RUN_DURATION_SECONDS', 300) 

        # Playwright选择器
        self.customer_service_button_selector = self.config.get('CUSTOMER_SERVICE_BUTTON_SELECTOR')
        self.iframe_selector = self.config.get('IFRAME_SELECTOR')
        self.chat_input_selector = self.config.get('CHAT_INPUT_SELECTOR')
        self.send_button_selector = self.config.get('SEND_BUTTON_SELECTOR')
        self.chat_message_area_selector = self.config.get('CHAT_MESSAGE_AREA_SELECTOR')
        self.customer_message_selector = self.config.get('CUSTOMER_MESSAGE_SELECTOR')

        # 重试配置 (虽然很多函数内部用了retry装饰器，但某些顶层操作也可能需要)
        self.max_retries = self.config.get('MAX_RETRIES', 3)
        self.retry_delay = self.config.get('RETRY_DELAY_SECONDS', 10)
        
        # 新增：浏览器实例重用计数器
        self.browser_usage_count = 0
        self.max_browser_usage = self.config.get('BROWSER_RESTART_INTERVAL_TASKS', 50)  # 每50个任务重启浏览器

    async def _check_system_resources(self):
        """检查系统资源状况，防止资源耗尽"""
        try:
            import psutil
            import os
            
            # 检查内存使用率
            memory_info = psutil.virtual_memory()
            if memory_info.percent > 90:
                logger.warning(f"[任务 {self.task_id}] 系统内存使用率过高: {memory_info.percent}%")
                
            # 检查打开的文件描述符数量
            process = psutil.Process(os.getpid())
            open_files_count = len(process.open_files())
            if open_files_count > 800:  # 接近系统限制时警告
                logger.warning(f"[任务 {self.task_id}] 当前进程打开文件数量较多: {open_files_count}")
                
            # 检查系统负载
            load_avg = psutil.getloadavg()[0] if hasattr(psutil, 'getloadavg') else 0
            cpu_count = psutil.cpu_count()
            if load_avg > cpu_count * 2:
                logger.warning(f"[任务 {self.task_id}] 系统负载过高: {load_avg}")
                
            # 强制垃圾收集
            import gc
            gc.collect()
            
        except ImportError:
            # 如果没有psutil，使用基本的检查
            import gc
            gc.collect()
            logger.debug(f"[任务 {self.task_id}] psutil未安装，跳过详细资源检查")
        except Exception as e:
            logger.debug(f"[任务 {self.task_id}] 系统资源检查时出错: {e}")

    async def _initialize_browser(self):
        """获取代理、UA并启动Playwright浏览器。"""
        logger.info(f"[任务 {self.task_id}] 初始化浏览器...")

        # 检查系统资源状况
        await self._check_system_resources()

        # 强制清理系统资源，防止资源泄漏
        from utils.system_cleanup import force_system_resource_cleanup
        force_system_resource_cleanup()

        # 检查浏览器实例使用计数，强制重启
        if hasattr(self, 'browser') and self.browser:
            self.browser_usage_count += 1
            if self.browser_usage_count >= self.max_browser_usage:
                logger.info(f"[任务 {self.task_id}] 浏览器使用次数达到限制({self.browser_usage_count}/{self.max_browser_usage})，强制重启")
                await self._force_restart_browser()
                self.browser_usage_count = 0
        
        proxy_info = None
        # 1. 获取代理IP
        try:
            proxy_params = self.config.get('PROXY_API_PARAMS', {})
            proxy_info = await asyncio.to_thread(
                get_proxy_ip, 
                self.config.get('PROXY_API_URL'), 
                self.config.get('PROXY_API_KEY'),
                proxy_params
            )
            if proxy_info:
              logger.info(f"[任务 {self.task_id}] 使用代理: {proxy_info['ip']}:{proxy_info['port']}")
        except (ProxyAPIError, NoAvailableProxyError, KeyError) as e:
            logger.error(f"[任务 {self.task_id}] 获取代理失败: {e}。")
            proxy_info = None
        except Exception as e:
            logger.error(f"[任务 {self.task_id}] 获取代理时发生意外错误: {e}。")
            proxy_info = None
        
        if proxy_info is None:
            logger.error(f"[任务 {self.task_id}] 未获取到代理，任务终止。")
            raise TaskRunError("无法获取代理IP，任务终止")

        # 2. 生成随机UA
        ua_string = get_random_user_agent(self.config.get('USER_AGENT_TYPE', 'pc'))
        logger.info(f"[任务 {self.task_id}] 使用 User-Agent: {ua_string}")

        # 3. 启动Playwright - 每个任务创建独立实例避免事件循环冲突
        # 不使用全局实例，每个任务创建独立的Playwright实例
        self.playwright = await async_playwright().start()
        logger.info(f"[任务 {self.task_id}] 创建独立的Playwright实例")

        # 严格的资源控制启动参数：优先考虑资源管理
        launch_args = [
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-setuid-sandbox',
            '--no-zygote',  # 禁用zygote进程，避免zygote相关错误
            '--disable-gpu',
            '--disable-gpu-sandbox',
            '--disable-software-rasterizer',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            '--disable-features=TranslateUI,VizDisplayCompositor',
            '--disable-ipc-flooding-protection',
            '--disable-background-networking',
            '--disable-sync',
            '--disable-default-apps',
            '--no-first-run',
            '--disable-extensions',
            '--disable-plugins',
            '--disable-component-extensions-with-background-pages',
            '--force-color-profile=srgb',
            '--memory-pressure-off',  # 关闭内存压力检测
            '--max_old_space_size=1024',  # 进一步减少V8堆内存限制
            '--disable-web-security',  # 禁用web安全策略
            '--disable-features=VizDisplayCompositor',
            '--disable-blink-features=AutomationControlled',  # 避免被检测为自动化

            # 严格的资源控制参数
            '--disable-video-capture',  # 禁用视频捕获
            '--disable-camera',  # 禁用摄像头
            '--disable-microphone',  # 禁用麦克风
            '--disable-accelerated-video-decode',  # 禁用视频解码加速
            '--disable-accelerated-video-encode',  # 禁用视频编码加速
            '--disable-notifications',  # 禁用通知
            '--disable-web-bluetooth',  # 禁用Web蓝牙
            '--disable-usb-keyboard-detect',  # 禁用USB键盘检测
            '--max-decoded-image-size-mb=32',  # 进一步限制图像解码大小
            '--renderer-process-limit=1',  # 限制渲染进程数量
            '--max-unused-resource-memory-usage-percentage=10',  # 进一步限制未使用资源内存

            # 严格的线程和进程控制
            '--thread-pool-size=2',  # 减少线程池大小
            '--max-concurrent-runs=1',  # 严格限制并发运行数
            '--num-raster-threads=1',  # 减少光栅化线程数
            '--force-device-scale-factor=1',  # 强制设备缩放因子

            # 平衡版：资源控制与功能保持的平衡
            '--disable-background-media-suspend',  # 禁用后台媒体挂起
            '--disable-audio-output',  # 禁用音频输出
            # 移除可能影响网站功能的参数
            # '--disable-media-stream',  # 移除，某些网站可能需要
            # '--disable-webgl',  # 移除，很多网站检测WebGL
            # '--disable-webgl2',  # 移除，可能被检测
            # '--disable-databases',  # 移除，可能影响正常功能
            # '--disable-local-storage',  # 移除，很多网站依赖localStorage
            # '--disable-session-storage',  # 移除，可能影响正常功能
            # '--disable-file-system',  # 移除，可能影响某些检测逻辑
            # '--single-process',  # 移除，可能被检测为自动化工具

            # 保留关键的资源控制参数
            '--max-tabs=1',  # 限制标签页数量
            '--max-windows=1',  # 限制窗口数量
        ]
        
        launch_options = {
            'headless': self.config.get('HEADLESS_BROWSER', True),
            'args': launch_args
        }
        
        if proxy_info:
            launch_options['proxy'] = {
                'server': f"http://{proxy_info['ip']}:{proxy_info['port']}"
            }
        
        try:
            # 首先启动浏览器
            self.browser = await self.playwright.chromium.launch(**launch_options)
            logger.info(f"[任务 {self.task_id}] 浏览器启动成功。")
            
            # 等待一小段时间确保浏览器完全启动
            await asyncio.sleep(0.5)
            
            # 创建浏览器上下文时也添加一些选项
            context_options = {
                'user_agent': ua_string,
                'viewport': {'width': 1366, 'height': 768},
                'ignore_https_errors': True,
                'java_script_enabled': True,
                'bypass_csp': True,  # 绕过内容安全策略
                'locale': 'zh-CN',
                'timezone_id': 'Asia/Shanghai',
                'permissions': ['geolocation'],  # 添加一些权限让浏览器看起来更真实
                'extra_http_headers': {
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Cache-Control': 'no-cache',
                    'Sec-Fetch-Dest': 'document',
                    'Sec-Fetch-Mode': 'navigate',
                    'Sec-Fetch-Site': 'none',
                    'Upgrade-Insecure-Requests': '1'
                }
            }
            
            self._context = await self.browser.new_context(**context_options)
            logger.info(f"[任务 {self.task_id}] 浏览器上下文创建成功。")
            
            # 创建页面
            self.page = await self._context.new_page()
            logger.info(f"[任务 {self.task_id}] 页面创建成功。")
            
            # 设置页面超时时间
            self.page.set_default_timeout(30000)
            self.page.set_default_navigation_timeout(30000)
            
            # 添加增强的反检测脚本
            await self.page.add_init_script("""
                // 隐藏webdriver属性
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                    configurable: true
                });
                
                // 添加chrome运行时对象
                window.chrome = {
                    runtime: {},
                    loadTimes: function() {},
                    csi: function() {}
                };
                
                // 模拟插件
                Object.defineProperty(navigator, 'plugins', {
                    get: () => {
                        return [
                            {name: 'Chrome PDF Plugin', filename: 'internal-pdf-viewer'},
                            {name: 'Chromium PDF Plugin', filename: 'libpdf.so'},
                            {name: 'Microsoft Edge PDF Plugin', filename: 'edge_pdf.dll'},
                            {name: 'WebKit built-in PDF', filename: 'WebKit built-in PDF'},
                            {name: 'Shockwave Flash', filename: 'pepflashplayer.dll'}
                        ];
                    },
                    configurable: true
                });
                
                // 覆盖权限查询
                const originalQuery = window.navigator.permissions.query;
                window.navigator.permissions.query = (parameters) => (
                    parameters.name === 'notifications' ?
                        Promise.resolve({ state: Notification.permission }) :
                        originalQuery(parameters)
                );
                
                // 添加语言数组
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['zh-CN', 'zh', 'en-US', 'en'],
                    configurable: true
                });
                
                // 隐藏自动化相关的错误
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
            """)
            
            logger.info(f"[任务 {self.task_id}] 浏览器和页面初始化成功。")
            
        except Exception as e:
            error_message = str(e)
            logger.error(f"[任务 {self.task_id}] 启动浏览器或创建页面失败: {e}")
            
            # 检查是否是zygote相关错误或浏览器关闭错误
            critical_errors = [
                'zygote', 'failed to launch', 'browser has been closed', 
                'context or browser has been closed', 'target page, context or browser has been closed'
            ]
            
            is_critical_error = any(error_phrase in error_message.lower() for error_phrase in critical_errors)
            
            if is_critical_error:
                logger.warning(f"[任务 {self.task_id}] 检测到关键浏览器错误，尝试强制清理并重试...")
                
                # 执行强制清理
                try:
                    await self._cleanup()  # 先清理当前实例
                    
                    from utils.system_cleanup import force_system_resource_cleanup
                    force_system_resource_cleanup()
                    
                    # 等待一些时间让系统恢复
                    await asyncio.sleep(5)  # 增加等待时间
                    
                    # 重新初始化 Playwright
                    self.playwright = await async_playwright().start()
                    
                    # 尝试重新启动（仅一次）
                    logger.info(f"[任务 {self.task_id}] 正在重试浏览器启动...")
                    
                    self.browser = await self.playwright.chromium.launch(**launch_options)
                    logger.info(f"[任务 {self.task_id}] 重试：浏览器启动成功。")
                    
                    await asyncio.sleep(0.5)
                    
                    context_options = {
                        'user_agent': ua_string,
                        'viewport': {'width': 1366, 'height': 768},
                        'ignore_https_errors': True,
                        'java_script_enabled': True,
                        'bypass_csp': True,
                        'locale': 'zh-CN',
                        'timezone_id': 'Asia/Shanghai'
                    }
                    
                    self._context = await self.browser.new_context(**context_options)
                    self.page = await self._context.new_page()
                    self.page.set_default_timeout(30000)
                    self.page.set_default_navigation_timeout(30000)
                    
                    logger.info(f"[任务 {self.task_id}] 重试成功，浏览器已启动。")
                    
                except Exception as retry_error:
                    logger.error(f"[任务 {self.task_id}] 重试启动浏览器也失败: {retry_error}")
                    await self._cleanup()
                    raise TaskRunError(f"启动浏览器失败（重试后）: {retry_error}") from retry_error
            else:
                await self._cleanup()
                raise TaskRunError(f"启动浏览器或创建页面失败: {e}") from e

    async def _simulate_page_scroll(self):
        logger.info(f"[任务 {self.task_id}] 模拟页面滚动...")
        try:
            # 大幅简化滚动操作，减少卡顿
            scroll_iterations = random.randint(3, 5)  # 减少滚动次数从8-13到3-5
            for i in range(scroll_iterations):
                scroll_amount = random.randint(300, 600)  # 增加单次滚动距离，减少总次数
                await self.page.mouse.wheel(0, scroll_amount)
                logger.debug(f"[任务 {self.task_id}] 滚动 {scroll_amount} 像素 ({i+1}/{scroll_iterations})。")
                await asyncio.sleep(random.uniform(0.1, 0.3))  # 大幅减少延迟从0.3-0.8到0.1-0.3
            logger.debug(f"[任务 {self.task_id}] 页面滚动完成。")
        except Exception as e:
            logger.warning(f"[任务 {self.task_id}] 页面滚动模拟期间出错: {e}")

    @retry_async(attempts=3, delay_seconds=10, logger_instance=logger, accepted_exceptions=(PlaywrightTimeoutError,))
    async def _navigate_and_open_chat(self):
        """获取动态URL，导航到目标页面，点击客服按钮，并切换到iframe。"""
        
        current_target_url = None
        try:
            logger.info(f"[任务 {self.task_id}] 开始获取动态目标URL...")
            current_target_url = await get_dynamic_target_url(
                api_endpoint=self.dynamic_url_api_endpoint,
                api_key=self.dynamic_url_api_key,
                project_names=self.dynamic_url_project_names
            )
            if not current_target_url:
                # This case should ideally be handled by get_dynamic_target_url raising an error
                raise TaskRunError("获取到的动态目标URL为空或无效。")
            logger.info(f"[任务 {self.task_id}] 成功获取动态URL: {current_target_url}")
        except (DynamicURLError, NoProjectNamesError) as e:
            logger.error(f"[任务 {self.task_id}] 获取动态目标URL失败: {e}")
            # This error should propagate up and cause the task to fail in run()
            # We re-raise it wrapped in TaskRunError if not already, or just let it propagate
            raise TaskRunError(f"获取动态URL失败: {e}") from e
        except Exception as e: # Catch any other unexpected error during URL fetching
            logger.critical(f"[任务 {self.task_id}] 获取动态目标URL时发生意外严重错误: {e}", exc_info=True)
            raise TaskRunError(f"获取动态URL时发生意外错误: {e}") from e

        logger.info(f"[任务 {self.task_id}] 导航到 {current_target_url}...")
        try:
            await self.page.goto(current_target_url, timeout=30000, wait_until='domcontentloaded')
            logger.info(f"[任务 {self.task_id}] 页面 DOM 内容已加载。")

            # 模拟页面滚动
            await self._simulate_page_scroll()
            logger.info(f"[任务 {self.task_id}] 页面滚动模拟完成。")
            '''           
            logger.info(f"[任务 {self.task_id}] 页面滚动模拟完成。正在点击客服按钮...")

            # 点击客服按钮
            if not self.customer_service_button_selector:
                raise TaskRunError("CUSTOMER_SERVICE_BUTTON_SELECTOR 未配置。")
            
            cs_button = self.page.locator(self.customer_service_button_selector)
            await cs_button.wait_for(state='visible', timeout=30000)
            await cs_button.click()
            logger.info(f"[任务 {self.task_id}] 客服按钮已点击。")

            # 等待并获取iframe
            if not self.iframe_selector:
                raise TaskRunError("IFRAME_SELECTOR 未配置。")
            
            logger.info(f"[任务 {self.task_id}] 等待聊天 iframe ({self.iframe_selector}) 加载...")
            # 等待iframe的宿主元素（例如 div#edu-bot-container2）出现，然后再定位iframe
            # 这个等待可以根据实际情况调整，如果iframe是动态插入的，需要等待其宿主元素
            await self.page.wait_for_selector(self.iframe_selector, state='attached', timeout=30000)
            self.chat_iframe = self.page.frame_locator(self.iframe_selector)
            
            # 等待iframe内部某个元素加载完成，表示iframe内容可用
            if not self.chat_input_selector:
                raise TaskRunError("用于 iframe 检查的 CHAT_INPUT_SELECTOR 未配置。")
            await self.chat_iframe.locator(self.chat_input_selector).wait_for(state='visible', timeout=30000)
            logger.info(f"[任务 {self.task_id}] 聊天 iframe 已准备就绪。")
            '''
        except PlaywrightTimeoutError as e:
            logger.error(f"[任务 {self.task_id}] 导航或打开聊天时超时: {e}")
            raise
        except Exception as e:
            logger.error(f"[任务 {self.task_id}] 导航或打开聊天时出错: {e}")
            raise TaskRunError(f"在 _navigate_and_open_chat 中出错: {e}") from e

    async def _get_new_customer_messages(self) -> list[str]:
        """获取客服发送的新消息。"""
        # 此方法需要根据实际页面结构和监听策略仔细实现
        logger.debug(f"[任务 {self.task_id}] 检查新客服消息...")
        if not self.chat_iframe:
            logger.warning(f"[任务 {self.task_id}] 聊天iframe不可用，无法获取消息。")
            return []
        
        try:
            # 等待消息区域可见
            await self.chat_iframe.locator(self.chat_message_area_selector).wait_for(state='visible', timeout=10000)
            
            all_message_elements = await self.chat_iframe.locator(self.customer_message_selector).all()
            
            new_messages_texts = []
            current_message_count = len(all_message_elements)

            if current_message_count > self.last_customer_message_count:
                logger.info(f"[任务 {self.task_id}] 检测到可能的新消息。当前总数: {current_message_count}，上次记录: {self.last_customer_message_count}")
                for i in range(self.last_customer_message_count, current_message_count):
                    # Playwright的Locator的text_content()是同步的，需要用evaluate或page.eval_on_selector
                    # 对于FrameLocator内的元素，可以直接调用 locator.text_content()
                    message_text = await all_message_elements[i].text_content()
                    if message_text:
                        message_text = message_text.strip()
                        # 避免重复添加相同的最后一条消息（如果页面有时会重新渲染）
                        is_duplicate_of_last_in_history = False
                        if self.chat_history and self.chat_history[-1]["role"] == "assistant":
                            if self.chat_history[-1]["content"] == message_text:
                                is_duplicate_of_last_in_history = True
                        
                        if message_text and not is_duplicate_of_last_in_history:
                            new_messages_texts.append(message_text)
                            logger.info(f"[任务 {self.task_id}] 收到客服消息 (前100字符): {message_text[:100]}...")
                self.last_customer_message_count = current_message_count
            else:
                logger.debug(f"[任务 {self.task_id}] No new messages. Current count: {current_message_count}, Last count: {self.last_customer_message_count}")
            
            return new_messages_texts
        except PlaywrightTimeoutError:
            logger.warning(f"[任务 {self.task_id}] 等待消息区域或消息超时。假设没有新消息。")
            return [] # 超时，认为没有新消息
        except Exception as e:
            logger.error(f"[任务 {self.task_id}] 获取客服消息出错: {e}")
            return [] # 出错，认为没有新消息

    @retry_async(attempts=3, delay_seconds=2, logger_instance=logger)
    async def _send_message_to_customer(self, message_text: str):
        """向客服发送消息"""
        try:
            logger.info(f"[任务 {self.task_id}] 准备向客服发送消息: {message_text[:100]}...")
            # 定位聊天输入框
            input_selector = self.config['CHAT_INPUT_SELECTOR']
            input_element = self.chat_iframe.locator(input_selector)
            await input_element.wait_for(state="visible", timeout=10000)
            # logger.info(f"[任务 {self.task_id}] 聊天输入框可见。")

            # 模拟逐字输入
            # logger.info(f"[任务 {self.task_id}] 开始模拟输入 (使用 page.keyboard.type)...")
            await input_element.click() # 先点击确保聚焦
            await asyncio.sleep(random.uniform(0.1, 0.3)) # 点击后短暂等待，以防页面JS响应
            
            for char in message_text:
                # 使用 page.keyboard.type，模仿用户参考代码
                # delay参数在 keyboard.type 中控制字符间的延迟（毫秒）
                await self.page.keyboard.type(char, delay=random.uniform(80, 250)) 
            # logger.info(f"[任务 {self.task_id}] 模拟输入完成。")

            # 模拟按下回车键发送
            await input_element.press("Enter")
            # logger.info(f"[任务 {self.task_id}] 已模拟回车键发送。")
            
            # 增加一个小延迟，确保消息有足够时间被JS处理和发送
            await asyncio.sleep(random.uniform(1, 2))

            # 更新已发送消息历史 - 我们发送的消息，角色是 assistant (AI本身)
            self.chat_history.append({"role": "assistant", "content": message_text})
            logger.info(f"[任务 {self.task_id}] 消息已发送。")
            return True
        except Exception as e:
            logger.error(f"[任务 {self.task_id}] 发送消息给客服失败: {e}。")
            return False

    def _build_openai_messages_from_history(self, current_customer_message: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        从self.chat_history构建发送给OpenAI的messages列表。
        可以包含一个可选的当前用户最新消息。
        """
        messages = []
        
        # 添加当前对话历史
        for chat_item in self.chat_history:
            messages.append(chat_item)
        
        # 如果提供了当前客服的消息，则添加它
        if current_customer_message:
            messages.append({"role": "user", "content": current_customer_message})
        
        return messages

    async def _chat_loop(self):
        """核心聊天逻辑，包括获取客服消息、调用OpenAI、发送回复。"""
        logger.info(f"[任务 {self.task_id}] 开始聊天循环，最大交互次数: {self.current_max_interactions}。")
        interactions_count = 0
        
        # 等待客服的第一条消息
        logger.info(f"[任务 {self.task_id}] 开始等待客服的初始消息...")
        initial_customer_messages = []
        
        # 从配置获取等待参数，或使用默认值
        max_initial_wait_time_seconds = self.config.get('CHAT_WAIT_INITIAL_MESSAGE_MAX_DELAY', 7)
        initial_poll_interval_min_seconds = 2 # 初始消息轮询的最小间隔（秒）
        initial_poll_interval_max_seconds = 5 # 初始消息轮询的最大间隔（秒）

        start_time = time.time()
        while time.time() - start_time < max_initial_wait_time_seconds:
            initial_customer_messages = await self._get_new_customer_messages()
            if initial_customer_messages:
                # logger.info(f"[任务 {self.task_id}] 成功获取到客服的初始消息。")
                break
            logger.debug(f"[任务 {self.task_id}] 暂未收到初始消息，等待 {initial_poll_interval_min_seconds}-{initial_poll_interval_max_seconds}秒后重试...")
            await asyncio.sleep(random.uniform(initial_poll_interval_min_seconds, initial_poll_interval_max_seconds))
        
        if not initial_customer_messages:
            logger.warning(f"[任务 {self.task_id}] 在 {max_initial_wait_time_seconds} 秒内未能获取到客服的初始消息。结束聊天。")
            return
        
        for msg_content in initial_customer_messages:
            # 客服的消息，角色是 user
            self.chat_history.append({"role": "user", "content": msg_content})
            # logger.info(f"[任务 {self.task_id}] 收到客服初始消息 (前100字符): {msg_content[:100]}...")

        while interactions_count < self.current_max_interactions:
            logger.info(f"[任务 {self.task_id}] 聊天交互 {interactions_count + 1}/{self.current_max_interactions}")

            if not self.chat_history or self.chat_history[-1]["role"] != "user":
                logger.warning(f"[任务 {self.task_id}] 对话历史中最后一条不是用户消息，或历史为空。尝试再次获取用户新消息。")
                await asyncio.sleep(random.uniform(self.config.get('CHAT_POLL_MIN_DELAY', 5), 
                                                  self.config.get('CHAT_POLL_MAX_DELAY', 10)))
                new_customer_messages = await self._get_new_customer_messages()
                if not new_customer_messages:
                    logger.info(f"[任务 {self.task_id}] 在等待回复时未检测到新的客服消息。")
                    if interactions_count > 0: 
                        logger.info(f"[任务 {self.task_id}] 认为客服没有更多回复，结束聊天。")
                        break
                    continue 

                for msg_content in new_customer_messages:
                    self.chat_history.append({"role": "user", "content": msg_content})
                    # logger.info(f"[任务 {self.task_id}] 收到客服消息 (前100字符): {msg_content[:100]}...")
                
                if not new_customer_messages: 
                    continue

            last_customer_message_content = self.chat_history[-1]["content"]

            min_length_for_openai = self.config.get('MIN_CUSTOMER_MESSAGE_LENGTH_FOR_OPENAI', 10)
            if len(last_customer_message_content) < min_length_for_openai:
                logger.info(f"[任务 {self.task_id}] 客服消息 '{last_customer_message_content[:30]}...' 过短 (长度 {len(last_customer_message_content)})，不调用 OpenAI。结束聊天。")
                break

            # 1. 构建发送给OpenAI的messages (包含system message和完整历史)
            system_message_content = self.config.get("OPENAI_SYSTEM_MESSAGE", "你是一个智能客服助手，你需要模拟真实用户和网页的在线客服进行沟通。请仅直接输出你需要发送给在线客服的文字内容，不要包含其他任何解释、说明或者markdown格式。")
            
            # 完整的对话历史，到客服的上一句话为止
            # self.chat_history 此时已经包含了客服的最新消息 (role: user)
            api_messages = [{"role": "system", "content": system_message_content}] + self.chat_history
            
            # logger.info(f"[任务 {self.task_id}] 准备发送给 OpenAI 的 messages: {api_messages}")


            # 2. 获取OpenAI响应
            # self.chat_history 已经包含了客服的最新消息，所以直接传递
            # logger.info(f"[任务 {self.task_id}] 获取OpenAI响应。传递给API的对话历史包含 {len(self.chat_history)} 条消息。")
            openai_response = await asyncio.to_thread(
                get_openai_response, 
                self.openai_api_key, 
                self.openai_model, 
                api_messages,
                base_url=self.openai_base_url
            )

            if openai_response:
                # logger.info(f"[任务 {self.task_id}] 收到OpenAI响应 (前100字符): {openai_response[:100]}...")
                # 3. 发送回复给客服 (AI的回复，会记录为 assistant)
                if await self._send_message_to_customer(openai_response):
                    interactions_count += 1
                    await asyncio.sleep(random.uniform(self.config.get('CHAT_USER_REPLY_MIN_DELAY', 2), 
                                                      self.config.get('CHAT_USER_REPLY_MAX_DELAY', 5)))
                else:
                    logger.error(f"[任务 {self.task_id}] 发送消息失败，中断聊天。")
                    break 
            else:
                logger.warning(f"[任务 {self.task_id}] 未能从OpenAI获取响应。结束聊天。")
                break
            
            if interactions_count >= self.current_max_interactions:
                logger.info(f"[任务 {self.task_id}] 已达到最大交互次数 {self.current_max_interactions}。")
                break

            logger.info(f"[任务 {self.task_id}] 等待客服下一轮回复...")
            await asyncio.sleep(random.uniform(self.config.get('CHAT_POLL_MIN_DELAY', 8), 
                                              self.config.get('CHAT_POLL_MAX_DELAY', 15)))
            
            new_customer_messages = await self._get_new_customer_messages()
            if not new_customer_messages:
                logger.info(f"[任务 {self.task_id}] 未检测到新的客服消息。结束聊天。")
                break
            
            for msg_content in new_customer_messages:
                # 客服的后续消息
                self.chat_history.append({"role": "user", "content": msg_content})
                logger.info(f"[任务 {self.task_id}] 收到客服消息 (前100字符): {msg_content[:100]}...")

        logger.info(f"[任务 {self.task_id}] 聊天循环结束。")

    async def _execute_core_task(self): # NEW INNER METHOD
        """包含任务核心执行步骤，以便被 asyncio.wait_for 包裹。"""
        await self._initialize_browser()
        await self._navigate_and_open_chat() # 客服按钮已在此方法内部点击

        # 点击客服按钮后，无论是否进行聊天，都等待一段时间
        post_click_wait_duration = random.uniform(self.post_click_wait_min_seconds, self.post_click_wait_max_seconds)
        logger.info(f"[任务 {self.task_id}] 客服按钮已点击。等待 {post_click_wait_duration:.2f} 秒...")
        await asyncio.sleep(post_click_wait_duration)

        if self.should_perform_chat_interaction:
            logger.info(f"[任务 {self.task_id}] 配置为执行聊天交互。")
            await self._chat_loop()
            # 聊天交互的成功依赖于是否发送过消息 (在外部 run 方法的 finally 块中判断)
        else:
            logger.info(f"[任务 {self.task_id}] 配置为不执行聊天交互。任务至此视为主要部分完成。")
            # 对于不聊天的任务，成功状态将在外部 run 方法中设置，如果这里没有抛异常
        
        # 任务完成后随机等待设定秒数 (这部分对于两种情况都适用)
        end_task_wait_min = self.config.get('END_TASK_WAIT_SECONDS_MIN', 1)
        end_task_wait_max = self.config.get('END_TASK_WAIT_SECONDS_MAX', 5)
        final_wait = random.uniform(end_task_wait_min, end_task_wait_max)
        logger.info(f"[任务 {self.task_id}] 核心任务逻辑完成。清理前等待 {final_wait:.2f} 秒。")
        await asyncio.sleep(final_wait)

    async def run(self) -> bool:
        """执行完整的任务流程。返回True表示任务成功，False表示失败。"""
        logger.info(f"[任务 {self.task_id}] 开始执行任务。是否执行聊天: {self.should_perform_chat_interaction}。最大执行时长: {self.max_task_run_duration_seconds if self.max_task_run_duration_seconds > 0 else '无限制'}s")
        task_successful = False
        e_raised: Optional[Exception] = None # 初始化 e_raised 为 None
        
        # 检查 max_task_run_duration_seconds 是否配置为有效值
        timeout_seconds = float(self.max_task_run_duration_seconds) if self.max_task_run_duration_seconds is not None and self.max_task_run_duration_seconds > 0 else None

        try:
            if timeout_seconds:
                logger.debug(f"[任务 {self.task_id}] 将使用 {timeout_seconds}s 的超时限制执行核心任务。")
                await asyncio.wait_for(self._execute_core_task(), timeout=timeout_seconds)
            else:
                logger.debug(f"[任务 {self.task_id}] 无超时限制执行核心任务。")
                await self._execute_core_task()

            if not self.should_perform_chat_interaction:
                task_successful = True
                logger.info(f"[任务 {self.task_id}] (非聊天模式) 核心任务执行成功。")

        except asyncio.TimeoutError as e_timeout: # Specific to asyncio.wait_for
            logger.error(f"[任务 {self.task_id}] 任务执行超过最大允许时长 ({timeout_seconds} 秒)，被强制终止。")
            task_successful = False # 超时则任务失败
            e_raised = e_timeout # 赋值给 e_raised
        except TaskRunError as e_task:
            logger.error(f"[任务 {self.task_id}] 任务运行错误: {e_task}")
            task_successful = False
            e_raised = e_task # 赋值给 e_raised
        except PlaywrightTimeoutError as e_playwright: # 被retry耗尽的Timeout
            logger.error(f"[任务 {self.task_id}] Playwright 重试后超时错误: {e_playwright}")
            task_successful = False
            e_raised = e_playwright # 赋值给 e_raised
        except Exception as e_general:
            logger.critical(f"[任务 {self.task_id}] 任务执行期间发生意外严重错误: {e_general}", exc_info=True)
            task_successful = False
            e_raised = e_general # 赋值给 e_raised
        finally:
            # 成功状态判断
            if self.should_perform_chat_interaction: 
                assistant_messages_count = sum(1 for msg in self.chat_history if msg.get("role") == "assistant")
                # 检查是否发生了超时，如果发生了，则 task_successful 应该已经是 False
                is_timeout_error = isinstance(e_raised, asyncio.TimeoutError)

                if assistant_messages_count > 0 and not is_timeout_error:
                    # 只有当没有发生超时，并且确实发送了消息，聊天任务才可能成功
                    # 同时，要确保没有其他类型的错误将 task_successful 设为 False
                    if task_successful is not False: # 如果之前没有被其他异常（非超时）设为False
                        task_successful = True
                        logger.info(f"[任务 {self.task_id}] (聊天交互模式) 检测到已发送 {assistant_messages_count} 条消息，任务标记为成功。")
                    # else: # 如果已经被其他异常设为False，保持False
                        # logger.info(f"[任务 {self.task_id}] (聊天交互模式) 虽然发送了 {assistant_messages_count} 条消息，但由于其他错误，任务仍标记为失败。")
                else:
                    # 如果未发送消息，或者发生了超时，或者 task_successful 已被明确设为False
                    if task_successful is not False and not is_timeout_error: # 如果不是因为超时，也不是因为其他错误，而是单纯没发消息
                         logger.warning(f"[任务 {self.task_id}] (聊天交互模式) 未检测到任何已发送的助手消息或任务未成功完成，任务标记为失败。")
                    # else: # 如果是因为超时或已被其他异常设为False，则日志已在except块中打印
                        # pass
                    task_successful = False # 确保最终是False
            # else 分支 (不执行聊天)，task_successful 已在 try 块的末尾或 except 块中设置

            await self._cleanup()
            logger.info(f"[任务 {self.task_id}] 任务执行完毕。最终成功状态: {task_successful}")
            return task_successful

    async def _cleanup(self):
        """清理Playwright资源。"""
        logger.info(f"[任务 {self.task_id}] 清理资源...")

        # 按照正确的顺序清理资源：page -> context -> browser -> playwright
        if self.page:
            try:
                await self.page.close()
                logger.debug(f"[任务 {self.task_id}] 页面已关闭。")
            except Exception as e:
                logger.error(f"[任务 {self.task_id}] 关闭页面时出错: {e}")

        # 清理浏览器上下文
        if hasattr(self, '_context') and self._context:
            try:
                await self._context.close()
                logger.debug(f"[任务 {self.task_id}] 浏览器上下文已关闭。")
            except Exception as e:
                logger.error(f"[任务 {self.task_id}] 关闭浏览器上下文时出错: {e}")

        if self.browser:
            try:
                # 关闭所有上下文和页面
                contexts = self.browser.contexts
                for context in contexts:
                    try:
                        await context.close()
                    except Exception as e:
                        logger.debug(f"[任务 {self.task_id}] 关闭上下文时出错: {e}")

                await self.browser.close()
                logger.info(f"[任务 {self.task_id}] 浏览器已关闭。")
            except Exception as e:
                logger.error(f"[任务 {self.task_id}] 关闭浏览器时出错: {e}")

        # 停止本任务创建的Playwright实例
        if self.playwright:
            try:
                await self.playwright.stop()
                logger.info(f"[任务 {self.task_id}] Playwright实例已停止。")
            except Exception as e:
                logger.error(f"[任务 {self.task_id}] 停止Playwright实例时出错: {e}")

        # 清空所有引用
        self.browser = None
        self.playwright = None
        self.page = None
        self.chat_iframe = None
        if hasattr(self, '_context'):
            self._context = None

        # 强制垃圾收集
        import gc
        gc.collect()

        # 执行系统资源清理
        try:
            from utils.system_cleanup import force_system_resource_cleanup
            force_system_resource_cleanup()
        except Exception as e:
            logger.warning(f"[任务 {self.task_id}] 清理时执行系统资源清理失败: {e}")

    async def _force_restart_browser(self):
        """强制重启浏览器实例，清理所有资源"""
        logger.warning(f"[任务 {self.task_id}] 执行浏览器强制重启...")
        
        try:
            # 1. 清理当前实例
            await self._cleanup()
            
            # 2. 强制清理系统资源
            from utils.system_cleanup import force_system_resource_cleanup
            force_system_resource_cleanup()
            
            # 3. 等待资源释放
            await asyncio.sleep(3)
            
            # 4. 重置状态
            self.browser = None
            self.context = None
            self.page = None
            self.playwright = None
            
            logger.info(f"[任务 {self.task_id}] 浏览器强制重启完成")
            
        except Exception as e:
            logger.error(f"[任务 {self.task_id}] 强制重启浏览器时出错: {e}")

# 示例：如何在外部调用TaskRunner (通常在main.py的线程池中)
async def main_example():
    # 假设这是从 config.yaml 加载的配置
    sample_config = {
        # 'TARGET_URL': 'YOUR_TARGET_URL_HERE', # 已移除，URL动态获取
        'PROXY_API_URL': 'http://your.proxy.api/get/',
        'PROXY_API_KEY': 'YOUR_PROXY_KEY_HERE',
        'OPENAI_API_KEY': 'YOUR_OPENAI_KEY_HERE',
        'OPENAI_MODEL': 'gpt-3.5-turbo',
        'OPENAI_BASE_URL': 'https://ark.cn-beijing.volces.com/api/v3', # 示例自定义baseURL
        'USER_AGENT_TYPE': 'pc',
        'MAX_CHAT_INTERACTIONS': 2,
        'HEADLESS_BROWSER': False, # 测试时可以打开浏览器看看
        'CUSTOMER_SERVICE_BUTTON_SELECTOR': '#ai-chat-button', # 根据实际页面填写
        'IFRAME_SELECTOR': '#edu-bot-iframe2', # 根据实际页面填写
        'CHAT_INPUT_SELECTOR': 'textarea.uni-textarea-textarea', # 根据实际页面填写
        'SEND_BUTTON_SELECTOR': 'uni-view.comfirm-btn', # 根据实际页面填写
        'CHAT_MESSAGE_AREA_SELECTOR': 'uni-scroll-view.message-container.chat-box div.uni-scroll-view-content',
        'CUSTOMER_MESSAGE_SELECTOR': 'uni-view.message-left uni-view.message-common.message-content',
        'INITIAL_MESSAGE_WAIT_SECONDS': 20,
        'CUSTOMER_REPLY_WAIT_SECONDS': 30,
        'MAX_RETRIES': 2,
        'RETRY_DELAY_SECONDS': 5,
        'MIN_CUSTOMER_MSG_LEN_FOR_OPENAI': 10,
        'MIN_INTERACTION_DELAY_SECONDS': 3,
        'MAX_INTERACTION_DELAY_SECONDS': 8,
        'END_TASK_WAIT_SECONDS_MIN': 3,
        'END_TASK_WAIT_SECONDS_MAX': 10,
        # 新增的配置项
        'CHAT_INTERACTION_PERCENTAGE_MIN': 0.2,
        'CHAT_INTERACTION_PERCENTAGE_MAX': 0.3,
        'POST_CLICK_WAIT_SECONDS_MIN': 3,
        'POST_CLICK_WAIT_SECONDS_MAX': 5,

        'PROXY_API_PARAMS': {'type': 0, 'isp': 0, 'distinct': 1, 'os': 1, 'cs': 1, 'is': 0, 'es': 0, 'textSep': 1, 'province': '', 'city': ''},
        'DYNAMIC_URL_API_ENDPOINT': 'YOUR_DYNAMIC_URL_API_ENDPOINT_HERE',
        'DYNAMIC_URL_API_KEY': 'YOUR_DYNAMIC_URL_API_KEY_HERE',
        'DYNAMIC_URL_PROJECT_NAMES': ['YOUR_PROJECT_NAME_1', 'YOUR_PROJECT_NAME_2'],
        'OPENAI_SYSTEM_MESSAGE': "You are a test assistant.",
    }

    # 进行基本配置检查
    # if 'YOUR_TARGET_URL_HERE' in sample_config.get('TARGET_URL', '') or \
    #     'YOUR_PROXY_KEY_HERE' in sample_config['PROXY_API_KEY'] or \
    #     'YOUR_OPENAI_KEY_HERE' in sample_config['OPENAI_API_KEY']:
    #     logger.warning("请替换 task_runner.py 测试代码中的 YOUR_PROXY_KEY_HERE 和 YOUR_OPENAI_KEY_HERE 为实际值以便测试。TARGET_URL 已移除。")
        # return

    # 检查动态URL配置是否完整，如果需要测试这部分
    # dynamic_url_keys_present = all(sample_config.get(k) for k in ['PROJECT_NAMES_FOR_DYNAMIC_URL', 'DYNAMIC_URL_API_ENDPOINT', 'DYNAMIC_URL_API_KEY'])
    # if not dynamic_url_keys_present:
    #     logger.warning("如果要测试动态URL获取，请在 task_runner.py 的 sample_config 中提供 PROJECT_NAMES_FOR_DYNAMIC_URL, DYNAMIC_URL_API_ENDPOINT, 和 DYNAMIC_URL_API_KEY。")
        # return # 或者选择不测试这部分，继续执行其他不依赖动态URL的初始化

    # 示例：手动决定这个runner是否执行聊天
    should_chat_for_this_task = random.random() < 0.5 # 模拟50%概率执行聊天，实际应由main.py决定
    p_instance = None
    try:
        p_instance = await async_playwright().start()
        runner = TaskRunner(playwright=p_instance, config=sample_config, task_id=999, should_perform_chat_interaction=should_chat_for_this_task)
        success = await runner.run()
        logger.info(f"Example task run finished with success: {success}")
    except Exception as e:
        logger.error(f"Error in main_example: {e}", exc_info=True)
    finally:
        if p_instance:
            await p_instance.stop()
            logger.info("Test Playwright instance stopped.")

if __name__ == '__main__':
    # 为了在 __main__ 中运行async代码:
    # asyncio.run(main_example())
    # 由于Playwright的async nature，直接运行此文件进行完整测试需要配置好URL和选择器。
    # 建议在main.py中通过线程池和事件循环来管理。
    logger.info("TaskRunner module loaded. To test, uncomment asyncio.run(main_example()) and fill in sample_config details.")
    logger.info("Ensure your target page selectors in config match the actual page for testing.") 