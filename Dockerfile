# Use an official Playwright Python image as a parent image
# Check for the latest stable version on MCR (Microsoft Container Registry) if needed
# Example: mcr.microsoft.com/playwright/python:v1.44.0-jammy
FROM mcr.microsoft.com/playwright/python:v1.44.0-jammy

# Set the working directory in the container
WORKDIR /app

# Set timezone to Asia/Shanghai (UTC+8)
ENV TZ=Asia/Shanghai
RUN apt-get update && \
    DEBIAN_FRONTEND=noninteractive apt-get install -y tzdata && \
    ln -sf /usr/share/zoneinfo/$TZ /etc/localtime && \
    echo $TZ > /etc/timezone && \
    dpkg-reconfigure -f noninteractive tzdata && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Copy the requirements file into the container at /app
COPY requirements.txt .

RUN python -m pip install --upgrade pip --default-timeout=100 -i https://pypi.tuna.tsinghua.edu.cn/simple
# Install any needed packages specified in requirements.txt
# --no-cache-dir reduces image size
RUN pip install --no-cache-dir -r requirements.txt --default-timeout=300 -i https://pypi.tuna.tsinghua.edu.cn/simple

# Note: The base Playwright image should have browsers pre-installed.
# If for some reason they are not, or you need a specific version/browser, you might run:
# RUN playwright install --with-deps chromium
# or RUN playwright install --with-deps
# However, with the official image, this is usually not needed.

# Copy the rest of the application code into the container at /app
# This includes main.py, task_runner.py, utils/, api_clients/, config.yaml (default)
COPY . .

# Ensure the entrypoint script is executable (if it were a shell script)
# For python, this is not strictly necessary for main.py

# Define environment variables that might be commonly overridden
# These are just examples; actual values would be set during `docker run`
# ENV LOG_LEVEL="INFO"
# ENV REDIS_HOST="redis_host_from_env"
# ENV OPENAI_API_KEY="your_openai_key_from_env"
# ... add others as needed for documentation or default behavior if not set by `docker run`

# Command to run the application
CMD ["python", "main.py"] 