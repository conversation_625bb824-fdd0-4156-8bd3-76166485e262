#!/usr/bin/env python3
"""
简单测试改进后的资源清理逻辑
"""

import sys
import yaml
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_basic_functionality():
    """测试基本功能"""
    logger.info("=== 测试基本功能 ===")
    
    try:
        # 测试配置加载
        with open('config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        logger.info("✓ 配置文件加载成功")
        
        # 测试新配置参数
        new_params = [
            'MIN_CHROME_RUNTIME_MINUTES',
            'TASK_AWARE_CLEANUP', 
            'ACTIVE_TASK_PROTECTION'
        ]
        
        for param in new_params:
            if param in config:
                logger.info(f"✓ {param}: {config[param]}")
            else:
                logger.error(f"✗ 缺少配置参数: {param}")
                return False
        
        # 测试系统清理模块
        from utils.system_cleanup import SystemCleanup, force_system_resource_cleanup
        logger.info("✓ 系统清理模块导入成功")
        
        # 创建清理实例
        cleanup = SystemCleanup(config)
        logger.info("✓ SystemCleanup实例创建成功")
        
        # 检查新属性
        if hasattr(cleanup, 'min_chrome_runtime_minutes'):
            logger.info(f"✓ 最小Chrome运行时间: {cleanup.min_chrome_runtime_minutes}分钟")
        else:
            logger.error("✗ 缺少min_chrome_runtime_minutes属性")
            return False
            
        if hasattr(cleanup, 'task_aware_cleanup'):
            logger.info(f"✓ 任务感知清理: {cleanup.task_aware_cleanup}")
        else:
            logger.error("✗ 缺少task_aware_cleanup属性")
            return False
            
        if hasattr(cleanup, 'active_task_protection'):
            logger.info(f"✓ 活跃任务保护: {cleanup.active_task_protection}")
        else:
            logger.error("✗ 缺少active_task_protection属性")
            return False
        
        # 测试强制清理函数
        logger.info("测试强制清理函数...")
        result = force_system_resource_cleanup()
        logger.info(f"✓ 强制清理完成，清理项目数: {result}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 基本功能测试失败: {e}")
        return False

def test_configuration_improvements():
    """测试配置改进"""
    logger.info("=== 测试配置改进 ===")
    
    try:
        with open('config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 验证清理间隔增加
        cleanup_interval = config.get('SYSTEM_CLEANUP_INTERVAL_MINUTES', 0)
        if cleanup_interval >= 15:
            logger.info(f"✓ 清理间隔适中: {cleanup_interval}分钟")
        else:
            logger.warning(f"⚠ 清理间隔可能过短: {cleanup_interval}分钟")
        
        # 验证Chrome运行时间限制
        max_runtime = config.get('MAX_CHROME_RUNTIME_MINUTES', 0)
        min_runtime = config.get('MIN_CHROME_RUNTIME_MINUTES', 0)
        
        if max_runtime > min_runtime and min_runtime >= 15:
            logger.info(f"✓ Chrome运行时间配置合理: {min_runtime}-{max_runtime}分钟")
        else:
            logger.warning(f"⚠ Chrome运行时间配置可能不合理: {min_runtime}-{max_runtime}分钟")
        
        # 验证进程限制
        max_processes = config.get('MAX_CHROME_PROCESSES', 0)
        if max_processes >= 3:
            logger.info(f"✓ Chrome进程限制适中: {max_processes}")
        else:
            logger.warning(f"⚠ Chrome进程限制可能过严: {max_processes}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 配置改进测试失败: {e}")
        return False

def test_task_runner_improvements():
    """测试任务执行器改进"""
    logger.info("=== 测试任务执行器改进 ===")
    
    try:
        # 检查task_runner.py中的改进
        with open('task_runner.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否包含新的清理逻辑
        if '_should_perform_cleanup' in content:
            logger.info("✓ 包含智能清理判断逻辑")
        else:
            logger.warning("⚠ 缺少智能清理判断逻辑")
        
        # 检查是否减少了强制清理调用
        force_cleanup_count = content.count('force_system_resource_cleanup()')
        logger.info(f"✓ 强制清理调用次数: {force_cleanup_count}")
        
        if force_cleanup_count <= 3:  # 应该减少调用次数
            logger.info("✓ 强制清理调用次数合理")
        else:
            logger.warning("⚠ 强制清理调用可能过于频繁")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 任务执行器改进测试失败: {e}")
        return False

def test_main_improvements():
    """测试主程序改进"""
    logger.info("=== 测试主程序改进 ===")
    
    try:
        # 检查main.py中的改进
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否提高了清理阈值
        if 'max_chrome_processes * 3' in content:
            logger.info("✓ 提高了Chrome进程清理阈值")
        else:
            logger.warning("⚠ 未找到提高的清理阈值")
        
        # 检查是否添加了条件清理
        if 'task_id_val % 5' in content:
            logger.info("✓ 添加了条件清理逻辑")
        else:
            logger.warning("⚠ 未找到条件清理逻辑")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 主程序改进测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("开始测试改进后的资源清理逻辑...")
    
    tests = [
        ("基本功能", test_basic_functionality),
        ("配置改进", test_configuration_improvements),
        ("任务执行器改进", test_task_runner_improvements),
        ("主程序改进", test_main_improvements),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n--- 执行测试: {test_name} ---")
        try:
            result = test_func()
            if result:
                logger.info(f"✓ {test_name} 通过")
                passed += 1
            else:
                logger.error(f"✗ {test_name} 失败")
        except Exception as e:
            logger.error(f"✗ {test_name} 异常: {e}")
    
    logger.info(f"\n=== 测试结果 ===")
    logger.info(f"通过: {passed}/{total}")
    
    if passed == total:
        logger.info("🎉 所有测试通过！资源清理逻辑改进成功")
        return True
    elif passed >= total * 0.75:
        logger.info("✅ 大部分测试通过，改进基本成功")
        return True
    else:
        logger.error("❌ 多数测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
