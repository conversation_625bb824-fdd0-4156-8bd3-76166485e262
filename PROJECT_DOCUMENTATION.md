# 项目文档：多线程网页客服聊天自动化工具

## 1. 项目概述

本项目旨在开发一个多线程自动化工具，模拟真人用户通过Playwright打开网页，与网页上的客服进行聊天。工具将能够：
- 使用代理IP和随机User-Agent打开网页。
- 在设定的时间段内执行任务。
- 控制每日执行的最大任务数量（优先通过Redis动态计算）。
- **根据配置的百分比，决定部分任务是否与客服进行完整的聊天交互。**
- 对于不进行完整聊天的任务，主要流程为打开页面、点击客服按钮并等待一段指定时间。
- 对于进行聊天的任务，每次实际最大交互次数会在1到配置的上限之间随机决定。
- 自动调用OpenAI API获取聊天回复。
- 任务执行间隔具有随机性，任务量会分散在设定的执行时间段内。
- 根据配置自动计算所需线程数。
- 使用Redis记录任务执行计数和动态配置。
- 对关键操作（如获取代理、打开网页、发送消息）设置重试机制。

## 2. 核心模块  

- **`main.py`**: 主控制模块。负责读取配置、计算线程数、初始化线程池、任务调度（包括决定哪些任务需要执行完整聊天）、与Redis交互更新计数、**每日动态计算 `MAX_DAILY_TASKS` (通过从Redis获取配置)**、程序启停管理。
- **`task_runner.py`**: 任务执行器模块。每个线程运行一个实例，负责单个任务流程。根据调度指令，或者仅完成"点击客服按钮并等待"，或者执行完整的"与客服聊天"会话。
- **`api_clients/`**: API客户端模块。
    - `proxy_client.py`: 封装获取代理IP的API调用逻辑。
    - `openai_client.py`: 封装调用OpenAI API的逻辑。
- **`utils/`**: 工具模块。
    - `ua_generator.py`: 随机User-Agent生成工具。
    - `redis_helper.py`: Redis操作辅助类。
    - `decorators.py`: 通用装饰器（如同步 `retry` 和异步 `retry_async`）。
    - `logger.py`: 日志配置模块。
- **`config.yaml`**: 配置文件，管理所有可配置参数。

## 3. 配置文件 (`config.yaml`) 详解

```yaml
# TARGET_URL: "your_target_website_url_here" # 目标网页 - 此配置已移除，URL通过API动态获取

# 代理IP API
PROXY_API_URL: "https://www.lthttp.com/iplist"
PROXY_API_KEY: "your_proxy_api_key"
PROXY_API_PARAMS: # 代理API的额外参数，例如：
#   type: 0
#   isp: 0
#   distinct: 1 # 是否去重

# OpenAI
OPENAI_API_KEY: "your_openai_api_key_here"
OPENAI_MODEL: "gpt-3.5-turbo"
OPENAI_BASE_URL: null # 可选，用于指定自定义的OpenAI API接入点
OPENAI_SYSTEM_MESSAGE: |  # OpenAI的系统消息，指导AI行为
  你现在是一个对当前客服发过来的话题感兴趣的普通人...
  (此处为详细的多行prompt内容)

# Redis
REDIS_HOST: "localhost"
REDIS_PORT: 6379
REDIS_DB: 0
REDIS_PASSWORD: null # 如果Redis有密码，请填写
REDIS_TASK_NAME_PREFIX: "chat_task" # Redis中存储任务计数相关的键名前缀
# 新增：用于动态计算MAX_DAILY_TASKS的Redis配置
REDIS_DYNAMIC_CONFIG_HASH_KEY: "form_submit_config"      # Redis中存储动态配置的Hash键名
REDIS_FIELD_MAX_SUBMISSIONS: "max_agent_submissions"   # Hash中代表最大提交数字段名
REDIS_FIELD_SUBMISSION_RATE: "agent_submission_rate" # Hash中代表提交比率字段名

# 任务控制
USER_AGENT_TYPE: "pc" # User-Agent类型: 'pc', 'mobile', 'tablet', 'random', 或具体浏览器名
HEADLESS_BROWSER: false # 是否以无头模式运行浏览器: true (无界面) 或 false (有界面)

# 任务调度时间窗口 (北京时间 HH:MM)
TASK_WINDOW_START_TIME: "05:00"
TASK_WINDOW_END_TIME: "23:00" # 实际执行到22:59:59

MAX_DAILY_TASKS: 100 # 每日最大任务总数。**此值现在主要作为系统启动时，或从Redis动态获取配置失败时的后备/初始参考值。实际每日任务上限会优先通过Redis动态计算得出。**
MAX_CHAT_INTERACTIONS: 3 # (如果执行聊天) 每个任务与客服的最大交互次数 (OpenAI调用次数)
ESTIMATED_SINGLE_TASK_DURATION_SECONDS: 300 # 预估单次任务耗时（秒），用于计算建议线程数

# 重试机制
MAX_RETRIES: 3 # 通用重试次数，部分操作可能有独立的重试设置
RETRY_DELAY_SECONDS: 10 # 通用重试间隔

# Playwright 元素选择器
CUSTOMER_SERVICE_BUTTON_SELECTOR: "#ai-chat-button"
IFRAME_SELECTOR: "#edu-bot-iframe2"
CHAT_INPUT_SELECTOR: "textarea.uni-textarea-textarea"
# SEND_BUTTON_SELECTOR: "uni-view.comfirm-btn" # 已不再使用，通过回车发送
CHAT_MESSAGE_AREA_SELECTOR: "uni-scroll-view.message-container.chat-box div.uni-scroll-view-content"
CUSTOMER_MESSAGE_SELECTOR: "uni-view.message-left uni-view.message-common.message-content"

# 聊天逻辑相关配置
MIN_CUSTOMER_MSG_LEN_FOR_OPENAI: 10 # (如果执行聊天) 客服消息的最小长度才调用OpenAI

# 各种随机延迟时间 (秒)
CHAT_WAIT_INITIAL_MESSAGE_MIN_DELAY: 3
CHAT_WAIT_INITIAL_MESSAGE_MAX_DELAY: 7
CHAT_POLL_MIN_DELAY: 8  # 获取客服后续回复的轮询等待下限
CHAT_POLL_MAX_DELAY: 15 # 获取客服后续回复的轮询等待上限
CHAT_USER_REPLY_MIN_DELAY: 2 # 我方发送消息后，等待客服反应的下限
CHAT_USER_REPLY_MAX_DELAY: 5 # 我方发送消息后，等待客服反应的上限
END_TASK_WAIT_SECONDS_MIN: 5 # 任务聊天循环结束后，清理前的等待下限
END_TASK_WAIT_SECONDS_MAX: 15# 任务聊天循环结束后，清理前的等待上限

# 日志配置
LOG_LEVEL: "INFO" # 可选: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_FILE: "chatbot_automation.log" # 日志文件名，若为 null 或空则只输出到控制台
LOG_MAX_BYTES: 10485760 # 单个日志文件最大大小 (10MB)
LOG_BACKUP_COUNT: 5    # 保留的旧日志文件数量

# ------------------------------------------------------------------------------
# 基本配置 (Essential URLs and Keys)
# ------------------------------------------------------------------------------

# TARGET_URL: "your_target_website_url_here" # 目标网页 - 此配置已移除，URL通过API动态获取

DYNAMIC_URL_API_ENDPOINT: "http://www.zhikaobibei.com/api3/get-article-url" # 获取目标URL的API的URL 
DYNAMIC_URL_API_KEY: "zk789012" # 获取目标URL的API的密钥

# ------------------------------------------------------------------------------
# 任务执行器具体配置 (Task Runner Specifics - task_runner.py)
# ------------------------------------------------------------------------------
MAX_TASK_RUN_DURATION_SECONDS: 300 # 单个任务最大允许执行时长（秒），0或负数表示不限制

# ------------------------------------------------------------------------------
# 聊天交互控制 (Chat Interaction Control - main.py & task_runner.py)
# ------------------------------------------------------------------------------

## 4. 单个任务执行流程

1.  **任务调度 (`main.py`)**:
    *   `main.py` 根据当日动态计算的 `MAX_DAILY_TASKS` 以及配置的 `CHAT_INTERACTION_PERCENTAGE_MIN` 和 `CHAT_INTERACTION_PERCENTAGE_MAX`，生成一个任务计划。
    *   此计划决定了当天计划提交的每个任务是否需要执行完整的聊天交互。
    *   当一个任务被调度执行时，`main.py` 会告知 `TaskRunner` 该任务是否需要进行聊天。

2.  **任务初始化 (`TaskRunner` 实例)**:
    *   接收到是否执行聊天的指令。
    *   读取 `MAX_TASK_RUN_DURATION_SECONDS` 配置，用于设置此任务的总体执行超时。
    *   如果确定要执行聊天，会根据配置的 `MAX_CHAT_INTERACTIONS` 随机生成一个本次任务实际的最大交互次数 (1 到 `MAX_CHAT_INTERACTIONS`之间)。

3.  **获取代理IP**: 调用 `proxy_client` 从指定API获取，具备重试机制。
4.  **生成随机UA**: 使用 `ua_generator` 根据 `USER_AGENT_TYPE` 配置生成。
5.  **启动Playwright**: 使用获取的代理IP和UA启动浏览器实例 (有头或无头模式根据配置)。
6.  **打开目标网页**: 每个任务执行前会通过 `dynamic_url_client` 从API动态获取目标URL，然后导航到该URL。此过程包括多次小幅度的页面滚动模拟，具备重试机制。
7.  **点击客服按钮**: 查找并点击 `CUSTOMER_SERVICE_BUTTON_SELECTOR` 指定的元素。
8.  **等待并切换到Iframe**: 等待 `IFRAME_SELECTOR` 指定的iframe加载完成，并将Playwright的上下文切换到此iframe。 *(如果后续不执行聊天，此iframe可能不会被深度使用)*

9. **根据调度指令执行后续操作**:
    *   **如果任务被标记为不执行聊天交互**:
        a.  在点击客服按钮并成功切换到iframe后，等待 `POST_CLICK_WAIT_SECONDS_MIN` 到 `POST_CLICK_WAIT_SECONDS_MAX` 之间的一个随机时长。
        b.  任务至此视为主要部分完成。
    *   **如果任务被标记为执行聊天交互**:
        a.  **聊天交互循环** (直到达到当前任务随机生成或设定的最大交互次数)：
            i.  **监听客服新消息**: 通过对比消息元素数量，等待 `CUSTOMER_MESSAGE_SELECTOR` 出现新的客服消息，并提取其文本内容。配置了 `INITIAL_MESSAGE_WAIT_SECONDS` 和 `CUSTOMER_REPLY_WAIT_SECONDS` 用于超时。
            ii. **调用OpenAI API**: 将获取的客服消息（如果长度大于`MIN_CUSTOMER_MSG_LEN_FOR_OPENAI`）与历史对话（包含`OPENAI_SYSTEM_MESSAGE`）一同通过 `openai_client` 发送给OpenAI，获取回复。
            iii.**回复客服**:
                1.  点击聊天输入框 (`CHAT_INPUT_SELECTOR`) 以确保焦点。
                2.  使用 `page.keyboard.type(char, delay=...)` 模拟逐字输入OpenAI生成的回复，每个字符间有随机延迟。
                3.  输入完毕后，模拟按下 "Enter" 键发送消息。
                4.  此发送操作具备异步重试机制。
            iv. 每次发送消息后，会等待 `MIN_INTERACTION_DELAY_SECONDS` 到 `MAX_INTERACTION_DELAY_SECONDS` 之间的随机时长。

10. **任务结束**: 完成上述流程后，统一随机等待 `END_TASK_WAIT_SECONDS_MIN` 到 `END_TASK_WAIT_SECONDS_MAX` 秒。
11. **资源清理**: 关闭Playwright浏览器。
12. **更新计数与成功判定**:
    *   **成功判定**:
        *   如果任务在设定的 `MAX_TASK_RUN_DURATION_SECONDS` 内完成：
            *   对于**不执行聊天**的任务：如果成功点击客服按钮并完成了指定的等待时长，任务被视为成功。
            *   对于**执行聊天**的任务：如果在聊天过程中至少成功向客服发送了一条消息，则任务被视为成功。
        *   如果任务执行超过 `MAX_TASK_RUN_DURATION_SECONDS`，则任务被视为失败。
    *   若任务成功，通过 `redis_helper` 通知Redis更新当日完成任务数。

## 5. 关键技术点

- **Playwright**:
    - 启动时配置代理和User-Agent。
    - 与iframe交互：`page.frame_locator(IFRAME_SELECTOR)`。
    - 元素定位和操作：`click`, `locator.press('Enter')`, `page.keyboard.type()` 等。
    - 页面滚动：`page.mouse.wheel()`。
- **随机UA**: 使用 `fake-useragent` 库。
- **代理IP**: API获取，并整合到Playwright。
- **任务调度**:
    - `main.py` 模块负责在 `START_HOUR` 和 `END_HOUR` 之间，根据每日动态计算得到的 `MAX_DAILY_TASKS` 和已完成任务数，动态计算任务提交间隔。
    - 它还负责根据 `CHAT_INTERACTION_PERCENTAGE_MIN` 和 `CHAT_INTERACTION_PERCENTAGE_MAX` 配置，确定每日应有多少任务执行完整的聊天交互，并将此决策传递给每个 `TaskRunner` 实例。
    - **每日 `MAX_DAILY_TASKS` 的动态计算**：在每日 `START_HOUR` 时（或按需重试时），系统会尝试从 Redis 的 `REDIS_DYNAMIC_CONFIG_HASH_KEY` (默认为 "form_submit_config") Hash 中获取 `REDIS_FIELD_MAX_SUBMISSIONS` (默认为 "max_agent_submissions") 和 `REDIS_FIELD_SUBMISSION_RATE` (默认为 "agent_submission_rate") 字段。
        - 计算公式：
            - 如果 `max_agent_submissions == 0` 或 `agent_submission_rate == 0`，则当日 `MAX_DAILY_TASKS = 0`。
            - 否则，基础计算值 `val = round(max_agent_submissions * (100 / agent_submission_rate - 1))`。
            - 最终 `MAX_DAILY_TASKS = val + round(val * 0.05)` (即基础值加上5%的缓冲量，结果不小于0)。
        - 如果从 Redis 获取或计算失败，会启用10分钟重试机制。在成功获取到当天的动态配置之前，当日 `MAX_DAILY_TASKS` 会被视为0（或使用配置文件中的后备值进行初始线程计算）。
    - (如果执行聊天) 单个任务的实际最大聊天交互次数在任务开始时从1到 `MAX_CHAT_INTERACTIONS` (配置值) 之间随机确定。
- **线程数计算**: 基于每日动态计算的 `MAX_DAILY_TASKS`（或其后备值）、执行时间窗口和 `ESTIMATED_SINGLE_TASK_DURATION_SECONDS` 自动估算。
- **Redis计数与配置**:
    - 使用原子性的 `INCR` 命令记录每日任务完成数。
    - Redis 还用于存储动态配置（通过 `REDIS_DYNAMIC_CONFIG_HASH_KEY` 指定的 Hash），以决定每日最大任务数。`redis_helper.py` 中的 `RedisHelper` 类新增了 `get_hash_fields` 方法用于从 Hash 中获取多个字段。
- **监听客服消息**: 通过轮询对比 `CUSTOMER_MESSAGE_SELECTOR` 元素的数量来检测新消息。
- **重试机制**: 使用 `@retry` (同步) 和 `@retry_async` (异步) 装饰器为关键IO操作提供重试能力。
- **模拟真人输入**: 通过 `page.keyboard.type(char, delay=...)` 实现逐字输入，并通过模拟回车键发送消息。

## 6. HTML 元素选择器（CSS Selectors）

- **客服按钮 (主页面)**: `CUSTOMER_SERVICE_BUTTON_SELECTOR: "#ai-chat-button"`
- **Iframe (主页面)**: `IFRAME_SELECTOR: "#edu-bot-iframe2"`
- **聊天输入框 (Iframe内)**: `CHAT_INPUT_SELECTOR: "textarea.uni-textarea-textarea"`
- **聊天消息区域 (Iframe内)**: `CHAT_MESSAGE_AREA_SELECTOR: "uni-scroll-view.message-container.chat-box div.uni-scroll-view-content"`
- **单条客服消息 (Iframe内)**: `CUSTOMER_MESSAGE_SELECTOR: "uni-view.message-left uni-view.message-common.message-content"`

## 7. Docker 部署与配置

本项目支持通过 Docker 进行容器化部署。以下是相关说明：

### 7.1. 构建 Docker 镜像

在项目根目录下，使用以下命令构建镜像：

```bash
docker build -t chatbot-automation .
```

### 7.2. 运行 Docker 容器

运行容器的基本命令如下：

```bash
docker run -d --name chatbot_instance chatbot-automation
```

**重要配置考虑：**

-   **配置文件 (`config.yaml`)**: 
    -   `Dockerfile` 会将项目中的 `config.yaml` 作为默认配置文件复制到镜像的 `/app/config.yaml`。
    -   **推荐做法**: 在生产环境中，通过 Docker 的绑定挂载 (bind mount) 来提供外部的 `config.yaml` 文件，以便于管理和修改配置而无需重新构建镜像。
        ```bash
        docker run -d --name chatbot_instance \
          -v /path/to/your/custom_config.yaml:/app/config.yaml \
          chatbot-automation
        ```
-   **环境变量覆盖**: 
    -   如 `load_config` 函数中所述，多个配置项可以通过环境变量进行覆盖。这对于传递敏感数据 (如API密钥) 或特定于环境的设置 (如Redis主机) 非常有用。
    -   环境变量名与 `config.yaml` 中的键名一致。例如，要覆盖 `REDIS_HOST`，可以在 `docker run` 命令中添加 `-e REDIS_HOST=your_redis_host`。
    -   **示例：使用环境变量覆盖部分配置并挂载自定义config.yaml**: 
        ```bash
        docker run -d --name chatbot_instance \
          -v /path/to/your/custom_config.yaml:/app/config.yaml \
          -e OPENAI_API_KEY="your_actual_openai_key" \
          -e REDIS_HOST="production_redis_server" \
          -e LOG_LEVEL="INFO" \
          chatbot-automation
        ```
    -   **可被环境变量覆盖的主要配置项包括**: `PROXY_API_URL`, `PROXY_API_KEY`, `OPENAI_API_KEY`, `OPENAI_MODEL`, `OPENAI_BASE_URL`, `REDIS_HOST`, `REDIS_PORT`, `REDIS_DB`, `REDIS_PASSWORD`, `REDIS_TASK_NAME_PREFIX`, `REDIS_DYNAMIC_CONFIG_HASH_KEY`, `REDIS_FIELD_MAX_SUBMISSIONS`, `REDIS_FIELD_SUBMISSION_RATE`, `USER_AGENT_TYPE`, `HEADLESS_BROWSER`, `LOG_LEVEL`, `MAX_DAILY_TASKS`, `MAX_CHAT_INTERACTIONS`, `ESTIMATED_SINGLE_TASK_DURATION_SECONDS`, `CHAT_INTERACTION_PERCENTAGE_MIN`, `CHAT_INTERACTION_PERCENTAGE_MAX`, `POST_CLICK_WAIT_SECONDS_MIN`, `POST_CLICK_WAIT_SECONDS_MAX`, `START_HOUR`, `END_HOUR`, `MIN_THREADS`, `MAX_THREADS`。

-   **网络**: 
    -   确保容器可以访问外部网络 (用于API调用) 以及您的 Redis 服务器。如果 Redis 也作为 Docker 容器运行，请使用 Docker 网络将它们连接起来，并通过服务名引用 Redis 主机。
    -   如果 Redis 在 Docker 主机上运行，容器内可能需要使用 `host.docker.internal` (在 Docker Desktop for Mac/Windows 中) 或主机的特定 IP 地址作为 `REDIS_HOST`。

-   **共享内存 (`/dev/shm`)**: 
    -   Playwright 使用的浏览器（特别是 Chromium）可能需要比 Docker 默认值 (64MB) 更大的共享内存。如果遇到浏览器崩溃或与共享内存相关的错误，请在运行容器时增加此值：
        ```bash
        docker run -d --name chatbot_instance --shm-size="1g" chatbot-automation 
        ```
        (根据需要调整大小，例如 `1g` 或 `2g`)。

-   **日志**: 
    -   默认情况下，如果 `config.yaml` 中的 `LOG_FILE` 未配置或配置为 null/空，日志将输出到容器的 `stdout`/`stderr`，这符合 Docker 的日志收集最佳实践。
    -   如果 `LOG_FILE` 配置了路径 (例如 `chatbot_automation.log`)，它将在容器内的 `/app/chatbot_automation.log` 创建。要持久化这些日志，可以将日志文件或其所在目录挂载到主机：
        ```bash
        docker run -d --name chatbot_instance \
          -v /path/on/host/for/logs:/app/logs_dir_in_container \
          # (假设您将 LOG_FILE 配置为 logs_dir_in_container/chatbot_automation.log)
          chatbot-automation 
        ```

### 7.3. Docker Compose (可选)

为了简化本地开发和多容器管理 (例如，如果您还想用 Docker 运行 Redis)，可以创建一个 `docker-compose.yml` 文件。

```yaml
#示例 docker-compose.yml
version: '3.8'
services:
  chatbot:
    build: .
    image: chatbot-automation # 可选，如果已手动构建则使用此名称
    container_name: chatbot_service
    volumes:
      - ./config.yaml:/app/config.yaml # 挂载实际的config.yaml
      # - ./logs:/app/logs # 挂载日志目录 (如果使用文件日志)
    environment:
      # - OPENAI_API_KEY=${OPENAI_API_KEY} # 从 .env 文件或shell环境读取
      # - REDIS_HOST=redis_in_compose # 如果在此compose中定义了redis服务
      # - LOG_LEVEL=DEBUG
      # 确保环境变量与config.yaml中的键名一致，例如：
      - PROXY_API_KEY=your_proxy_key_if_different_from_config
      - OPENAI_API_KEY=your_openai_key_if_different_from_config
      - REDIS_PASSWORD=your_redis_password_if_needed
    shm_size: '1gb' # 设置共享内存
    # depends_on:
    #   - redis_in_compose # 如果在compose中定义了redis
    networks:
      - chatbot_net

  # redis_in_compose: # 可选的Redis服务示例
  #   image: "redis:alpine"
  #   container_name: redis_for_chatbot
  #   networks:
  #     - chatbot_net
  #   volumes:
  #     - ./redis_data:/data # 持久化Redis数据

networks:
  chatbot_net:
    driver: bridge
```

要使用 Docker Compose, 您通常会在 `docker-compose.yml` 所在目录运行 `docker-compose up -d`。

----------------
此文档将随项目进展而更新。 