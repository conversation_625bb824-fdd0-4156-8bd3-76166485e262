import yaml # 确保导入PyYAML库
import time
import datetime
import asyncio
import concurrent.futures
import signal
import os
import math
import random
import threading
from typing import Optional, Any, List

from utils.logger import logger
from utils.redis_helper import RedisHelper, RedisConnectionError
from utils.system_cleanup import SystemCleanup, force_system_resource_cleanup
from auto_health_monitor import AutoHealthMonitor
from task_runner import TaskRunner # Assuming TaskRunner is in task_runner.py
from playwright.async_api import Playwright, async_playwright

# 全局关闭事件，用于优雅地停止所有任务和线程
shutdown_event = threading.Event()
# 全局 Playwright 实例
playwright_instance: Optional[Playwright] = None

def _get_env_var(var_name: str, default_value: Any, expected_type: type) -> Any:
    """Helper function to get environment variable and cast to expected type."""
    value_str = os.getenv(var_name)
    if value_str is None:
        return default_value
    
    try:
        if expected_type == bool:
            if value_str.lower() == 'true':
                return True
            elif value_str.lower() == 'false':
                return False
            else:
                logger.warning(f"环境变量 '{var_name}' 的值 '{value_str}' 不是有效的布尔值 (true/false)。使用默认值: {default_value}")
                return default_value
        elif expected_type == int:
            return int(value_str)
        elif expected_type == float:
            return float(value_str)
        # Add other type conversions if needed, e.g., list from comma-separated string
        return value_str # Defaults to string if no other type matches
    except ValueError:
        logger.warning(f"无法将环境变量 '{var_name}' 的值 '{value_str}' 转换为 {expected_type.__name__}。使用默认值: {default_value}")
        return default_value

def load_config(config_path='config.yaml') -> dict:
    """加载YAML配置文件，并允许环境变量覆盖特定项。"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        logger.info(f"配置文件 {config_path} 加载成功。")
    except FileNotFoundError:
        logger.error(f"配置文件 {config_path} 未找到。将尝试仅使用环境变量或默认值。")
        config = {} # Start with an empty config if file not found
    except yaml.YAMLError as e:
        logger.error(f"解析配置文件 {config_path} 失败: {e}。将尝试仅使用环境变量或默认值，但这可能导致功能不完整。")
        config = {}
    except Exception as e:
        logger.error(f"加载配置时发生意外错误: {e}。将尝试仅使用环境变量或默认值。")
        config = {}

    # 定义可以被环境变量覆盖的配置项及其期望类型和默认值（如果YAML中没有）
    # (key_in_yaml, env_var_name, type, default_if_not_in_yaml_and_not_in_env)
    # 通常 env_var_name 与 key_in_yaml 相同
    env_overrides_spec = [
        # ('TARGET_URL', str, "your_target_website_url_here"), # 已移除
        ('PROXY_API_URL', str, None),
        ('PROXY_API_KEY', str, None),
        ('OPENAI_API_KEY', str, None),
        ('OPENAI_MODEL', str, 'gpt-3.5-turbo'),
        ('OPENAI_BASE_URL', str, None), # 允许None
        ('REDIS_HOST', str, 'localhost'),
        ('REDIS_PORT', int, 6379),
        ('REDIS_DB', int, 0),
        ('REDIS_PASSWORD', str, None), # 允许None
        ('REDIS_TASK_NAME_PREFIX', str, 'chat_task'),
        ('REDIS_DYNAMIC_CONFIG_HASH_KEY', str, 'form_submit_config'),
        ('REDIS_FIELD_MAX_SUBMISSIONS', str, 'max_form_submissions'),
        ('REDIS_FIELD_SUBMISSION_RATE', str, 'agent_submission_rate'),
        ('USER_AGENT_TYPE', str, 'pc'),
        ('HEADLESS_BROWSER', bool, True),
        ('TASK_WINDOW_START_TIME', str, '05:00'), # 保持字符串，后续解析
        ('TASK_WINDOW_END_TIME', str, '23:00'),   # 保持字符串，后续解析
        ('MAX_DAILY_TASKS', int, 100),
        ('MAX_CHAT_INTERACTIONS', int, 5),
        ('ESTIMATED_SINGLE_TASK_DURATION_SECONDS', int, 300),
        ('LOG_LEVEL', str, 'INFO'),
        ('CHAT_INTERACTION_PERCENTAGE_MIN', float, 0.2),
        ('CHAT_INTERACTION_PERCENTAGE_MAX', float, 0.3),
        # 注意: 对于复杂的嵌套结构如 PROXY_API_PARAMS 或多行字符串 OPENAI_SYSTEM_MESSAGE，
        # 通过简单环境变量覆盖比较困难，它们通常从YAML加载或通过挂载的配置文件管理。
        # 这里我们主要覆盖顶层或易于管理的简单类型键。
    ]

    for key, var_type, default_val_if_missing_everywhere in env_overrides_spec:
        yaml_value = config.get(key, default_val_if_missing_everywhere) # Get from YAML or ultimate default
        # Use key itself as the environment variable name
        config[key] = _get_env_var(key, yaml_value, var_type)
        if os.getenv(key) is not None:
            logger.info(f"配置项 '{key}' 已被环境变量覆盖为: {config[key]}") 

    # 确保关键但可能未在env_overrides_spec中通过默认值完全初始化的配置项存在，如果YAML加载失败
    # 例如，那些没有简单默认值的，或者必须存在的
    if 'OPENAI_SYSTEM_MESSAGE' not in config:
        config['OPENAI_SYSTEM_MESSAGE'] = "You are a helpful assistant." # 一个非常基础的默认
        logger.warning("OPENAI_SYSTEM_MESSAGE 未在 config.yaml 或环境变量中配置，使用基础默认值。")
    
    # 移除 TARGET_URL 从关键配置项检查
    if not all(k in config for k in ['PROXY_API_URL', 'PROXY_API_KEY', 'OPENAI_API_KEY']):
        logger.warning("重要配置项可能缺失（例如 API Keys）。请检查 config.yaml 或环境变量设置。")
    
    return config

def calculate_optimal_threads(config: dict, current_max_daily_tasks: int) -> int:
    """根据配置和当日最大任务数计算推荐的线程数。"""
    # max_daily_tasks = config.get('MAX_DAILY_TASKS', 100) # 从 config 中读取的值不再直接使用
    start_hour = config.get('START_HOUR', 0)
    end_hour = config.get('END_HOUR', 23) # 23表示到22:59:59结束
    estimated_single_task_duration_seconds = config.get('ESTIMATED_SINGLE_TASK_DURATION_SECONDS', 300)

    if start_hour >= end_hour:
        logger.warning("START_HOUR 大于或等于 END_HOUR。假设为24小时窗口计算线程数，但这可能影响调度。")
        if start_hour == end_hour:
            total_active_hours = 23 
        else: 
            total_active_hours = (24 - start_hour) + end_hour
    else:
        total_active_hours = end_hour - start_hour

    if total_active_hours <= 0:
        logger.warning(f"计算的总活动小时为 {total_active_hours}，线程计算默认使用1小时。")
        total_active_hours = 1 
    
    if estimated_single_task_duration_seconds <= 0:
        logger.warning("ESTIMATED_SINGLE_TASK_DURATION_SECONDS 无效，线程计算默认使用300秒。")
        estimated_single_task_duration_seconds = 300

    total_active_seconds_in_window = total_active_hours * 3600
    
    # 单个线程在整个时间窗口内能处理的任务数
    tasks_per_thread_in_window = total_active_seconds_in_window / estimated_single_task_duration_seconds
    if tasks_per_thread_in_window <= 0: # Should not happen if inputs are positive
        logger.warning("tasks_per_thread_in_window 为零或负数，默认使用1个线程。")
        return 1

    num_threads = math.ceil(current_max_daily_tasks / tasks_per_thread_in_window) if current_max_daily_tasks > 0 else 1
    
    # 设置一个合理的最小和最大线程数限制
    min_threads = config.get('MIN_THREADS', 1)
    max_threads = config.get('MAX_THREADS', os.cpu_count() or 4) # 默认为CPU核心数或4
    
    calculated_threads = max(min_threads, min(num_threads, max_threads))
    # 如果 current_max_daily_tasks 为 0，则线程数也应为最少（例如1），以保持调度器运行但不提交任务
    if current_max_daily_tasks == 0:
        calculated_threads = min_threads
        logger.info(f"当日最大任务数为0，线程数设置为最小值: {calculated_threads}")
    else:
        logger.info(f"计算得到的最佳线程数: {calculated_threads} (基于 {current_max_daily_tasks} 个任务, 原始值: {num_threads}, 最小值: {min_threads}, 最大值: {max_threads})")
    return calculated_threads


def worker_task_wrapper(p: Playwright, config_dict: dict, task_id_val: int, redis_helper_instance: RedisHelper, daily_max_tasks_at_submission: int, should_perform_chat_interaction: bool):
    """包装器函数，供线程池中的每个线程执行。"""
    if shutdown_event.is_set():
        logger.warning(f"[工作线程-{task_id_val}] 收到关闭信号，任务未启动。")
        return

    logger.info(f"[工作线程-{task_id_val}] 开始任务。是否执行聊天: {should_perform_chat_interaction}")

    # 任务开始前检查系统资源
    try:
        from utils.system_cleanup import force_system_resource_cleanup
        import psutil

        # 检查Chrome进程数量
        chrome_count = 0
        for proc in psutil.process_iter(['name']):
            try:
                if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                    chrome_count += 1
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue

        max_chrome_processes = config_dict.get('MAX_CHROME_PROCESSES', 2)
        if chrome_count > max_chrome_processes:
            logger.warning(f"[工作线程-{task_id_val}] 检测到过多Chrome进程({chrome_count}>{max_chrome_processes})，执行清理")
            force_system_resource_cleanup()
        elif chrome_count > 0:
            logger.info(f"[工作线程-{task_id_val}] 当前Chrome进程数量: {chrome_count}（正常范围）")

    except Exception as e:
        logger.warning(f"[工作线程-{task_id_val}] 任务前资源检查失败: {e}")

    task_runner = TaskRunner(
        playwright=None,  # 不传入全局实例，让每个任务创建独立实例
        config=config_dict,
        task_id=task_id_val,
        should_perform_chat_interaction=should_perform_chat_interaction
    )
    success = False
    try:
        # asyncio.run() 会创建和管理自己的事件循环
        success = asyncio.run(task_runner.run())
    except Exception as e:
        logger.error(f"[工作线程-{task_id_val}] 任务执行期间发生严重错误: {e}", exc_info=True)
        success = False # 确保在异常时success为False
    finally:
        if success:
            logger.info(f"[工作线程-{task_id_val}] 任务成功完成。正在增加 Redis 计数器。")
            try:
                # 使用配置中定义的 task_name_prefix，如果存在的话
                task_name_prefix = config_dict.get('REDIS_TASK_NAME_PREFIX', 'chat_task')
                redis_helper_instance.increment_daily_task_count(task_name_prefix=task_name_prefix)
            except RedisConnectionError as rce:
                logger.error(f"[工作线程-{task_id_val}] 增加计数器时 Redis 连接错误: {rce}")
            except Exception as e_redis:
                logger.error(f"[工作线程-{task_id_val}] 增加 Redis 计数器失败: {e_redis}", exc_info=True)
        else:
            logger.warning(f"[工作线程-{task_id_val}] 任务失败或未成功完成。")

        # 任务结束后强制清理资源
        try:
            from utils.system_cleanup import force_system_resource_cleanup
            force_system_resource_cleanup()
        except Exception as e:
            logger.warning(f"[工作线程-{task_id_val}] 任务后资源清理失败: {e}")

        # 在日志中添加任务提交时的当日最大任务数
        logger.info(f"[工作线程-{task_id_val}] 处理完毕。(提交时当日最大任务数: {daily_max_tasks_at_submission})")

def handle_signal(signum, frame):
    """处理操作系统信号，触发优雅关闭。"""
    logger.warning(f"收到信号 {signal.Signals(signum).name}。开始优雅关闭...")
    shutdown_event.set()

def _get_dynamic_max_tasks_from_redis(redis_helper: RedisHelper, config: dict) -> int:
    """
    从 Redis 获取并计算动态的 MAX_DAILY_TASKS。

    Args:
        redis_helper: RedisHelper 实例。
        config: 配置字典。

    Returns:
        计算得到的当日最大任务数 (int, >= 0)。
        如果在获取或计算过程中发生非连接错误（如字段缺失、值无效），则返回 0。

    Raises:
        RedisConnectionError: 如果与 Redis 的连接失败。
        Exception: 其他底层 Redis 操作可能抛出的异常。
    """
    redis_config_key = config.get('REDIS_DYNAMIC_CONFIG_HASH_KEY', 'form_submit_config')
    field_max_submissions = config.get('REDIS_FIELD_MAX_SUBMISSIONS', 'max_form_submissions')
    field_submission_rate = config.get('REDIS_FIELD_SUBMISSION_RATE', 'agent_submission_rate')

    logger.info(f"尝试从 Redis Hash '{redis_config_key}' 获取字段: {field_max_submissions}, {field_submission_rate}")
    temp_max_tasks = 0 # 默认任务数为0

    try:
        # get_hash_fields 本身会处理连接错误并可能抛出 RedisConnectionError
        redis_values = redis_helper.get_hash_fields(
            redis_config_key,
            [field_max_submissions, field_submission_rate]
        )
        max_submissions_str = redis_values.get(field_max_submissions)
        submission_rate_str = redis_values.get(field_submission_rate)

        if max_submissions_str is not None and submission_rate_str is not None:
            logger.info(f"从 Redis 获取到: {field_max_submissions}={max_submissions_str}, {field_submission_rate}={submission_rate_str}")
            try:
                m_val = int(max_submissions_str)
                #r_val = int(submission_rate_str)
                r_val = random.randint(2, 5) # 随机生成2-5之间的整数

                if m_val == 0 or r_val == 0:
                    temp_max_tasks = 0
                    logger.info(f"根据 Redis 值 (M={m_val}, R={r_val})，当日 MAX_DAILY_TASKS 计算为 0。")
                elif m_val > 0 and r_val > 0 : # r_val 不能为零以避免除零
                    calculated_value = round(m_val * (100 / r_val - 1))
                    # 添加 10% 的缓冲量
                    buffer_value = round(calculated_value * 0.1)
                    temp_max_tasks = max(0, calculated_value + buffer_value) # 确保不为负
                    logger.info(f"根据 Redis 值 (M={m_val}, R={r_val})，基础计算值为 {calculated_value}，添加 10% 缓冲量 {buffer_value}，最终 MAX_DAILY_TASKS 为 {temp_max_tasks}。")
                else: # m_val < 0 or r_val < 0 (不预期的情况)
                    logger.warning(f"Redis 中的 {field_max_submissions} ({m_val}) 或 {field_submission_rate} ({r_val}) 为负数，当日 MAX_DAILY_TASKS 设为 0。")
                    temp_max_tasks = 0
                
            except ValueError:
                logger.warning(f"无法将 Redis 中的 {field_max_submissions} ('{max_submissions_str}') 或 {field_submission_rate} ('{submission_rate_str}') 转换为整数。当日任务数视为 0。")
                temp_max_tasks = 0 # 解析失败，当日任务数为0
        else:
            missing_fields = []
            if max_submissions_str is None: missing_fields.append(field_max_submissions)
            if submission_rate_str is None: missing_fields.append(field_submission_rate)
            logger.warning(f"Redis Hash '{redis_config_key}' 中缺少字段: {', '.join(missing_fields)}。当日任务数视为 0。")
            temp_max_tasks = 0 # 字段缺失，当日任务数为0

    # 注意：RedisConnectionError 由 get_hash_fields 抛出，这里不需要捕获再抛出
    # 我们只捕获特定于此函数逻辑的错误（如ValueError），并将其他 Redis 异常（如果 get_hash_fields 内部未处理）传递出去
    except Exception as e:
        # 捕获除 RedisConnectionError 之外的意外错误（尽管 get_hash_fields 应该处理大部分）
        logger.error(f"处理 Redis 获取的 MAX_DAILY_TASKS 数据时发生意外错误: {e}", exc_info=True)
        temp_max_tasks = 0 # 发生意外错误，安全起见设为0

    logger.info(f"动态 MAX_DAILY_TASKS 计算结果: {temp_max_tasks}")
    return temp_max_tasks

def _update_task_chat_plan(config: dict, current_max_daily_tasks_today: int, logger_instance, redis_helper: RedisHelper, task_name_prefix: str, current_date: datetime.date) -> tuple[list[bool], int]:
    """辅助函数：生成或更新当日的聊天交互计划，并与Redis同步。"""
    num_chat_tasks_final = 0

    if current_max_daily_tasks_today is None or current_max_daily_tasks_today <= 0:
        logger_instance.info(f"日期 {current_date.isoformat()}: 当日最大任务数为0或未定义，聊天交互任务数设为0。")
        num_chat_tasks_final = 0
        # 即使任务数为0，也应在Redis中记录，以表明已为当日处理过
        redis_helper.set_daily_chat_interaction_state(current_date, task_name_prefix, 0, 0)
    else:
        stored_state = redis_helper.get_daily_chat_interaction_state(current_date, task_name_prefix)

        if stored_state is not None:
            stored_chat_count, stored_max_tasks_basis = stored_state
            if stored_max_tasks_basis == current_max_daily_tasks_today:
                num_chat_tasks_final = stored_chat_count
                logger_instance.info(f"日期 {current_date.isoformat()}: 使用Redis中存储的聊天交互任务数: {num_chat_tasks_final} (基于总任务数 {current_max_daily_tasks_today})。")
            else:
                logger_instance.info(f"日期 {current_date.isoformat()}: Redis中存储的聊天交互任务数基准 ({stored_max_tasks_basis}) 与当前总任务数 ({current_max_daily_tasks_today}) 不匹配。重新计算。")
                # Fall through to recalculate
        else:
            chat_percentage_min = config.get('CHAT_INTERACTION_PERCENTAGE_MIN', 0.2)
            chat_percentage_max = config.get('CHAT_INTERACTION_PERCENTAGE_MAX', 0.3)
            chat_percentage_min = max(0.0, min(1.0, chat_percentage_min))
            chat_percentage_max = max(chat_percentage_min, min(1.0, chat_percentage_max))
            
            num_chat_tasks_float = current_max_daily_tasks_today * random.uniform(chat_percentage_min, chat_percentage_max)
            num_chat_tasks_final = round(num_chat_tasks_float)
            
            logger_instance.info(f"日期 {current_date.isoformat()}: 基于当前总任务数 {current_max_daily_tasks_today}，新计算的聊天交互任务数为: {num_chat_tasks_final}。将存入Redis。")
            redis_helper.set_daily_chat_interaction_state(current_date, task_name_prefix, num_chat_tasks_final, current_max_daily_tasks_today)

    # 生成实际的任务分配计划
    if current_max_daily_tasks_today is not None and num_chat_tasks_final > current_max_daily_tasks_today:
        logger_instance.warning(f"计算/获取的聊天任务数 ({num_chat_tasks_final}) 大于当日总任务数 ({current_max_daily_tasks_today})。将聊天任务数调整为总任务数。")
        num_chat_tasks_final = current_max_daily_tasks_today
        
    new_task_chat_plan = [True] * num_chat_tasks_final + [False] * (current_max_daily_tasks_today - num_chat_tasks_final if current_max_daily_tasks_today is not None and current_max_daily_tasks_today > 0 else 0)
    random.shuffle(new_task_chat_plan)
    tasks_scheduled_today_count = 0 # 重置当日已调度计数器
    logger_instance.info(f"日期 {current_date.isoformat()}: 为 {current_max_daily_tasks_today if current_max_daily_tasks_today is not None else 0} 个任务生成了聊天交互计划: {num_chat_tasks_final} 个任务将进行聊天。")
    return new_task_chat_plan, tasks_scheduled_today_count

def main_scheduler():
    """主调度逻辑，管理任务的创建和分发。"""
    global playwright_instance
    logger.info("主调度器启动中...")
    config = None # 初始化为None
    try:
        config = load_config()
    except Exception as e_load: # 捕获load_config抛出的任何异常
        logger.error(f"!!!!!! load_config 内部直接抛出的异常: {type(e_load).__name__} - {e_load} !!!!!!") # 打印详细的原始异常
        logger.error("主调度器因无法加载配置而终止。")
        return

    # 初始化系统清理
    system_cleanup = None
    if config.get('ENABLE_SYSTEM_CLEANUP', True):
        try:
            system_cleanup = SystemCleanup(config)
            system_cleanup.start_cleanup_daemon()
        except Exception as e:
            logger.error(f"启动系统清理守护进程失败: {e}")

    # 初始化自动健康监控
    health_monitor = None
    if config.get('AUTO_HEALTH_MONITORING_ENABLED', True):
        try:
            health_monitor = AutoHealthMonitor(config)
            health_monitor.start_monitoring()
            logger.info("自动健康监控已启动")
        except Exception as e:
            logger.error(f"启动自动健康监控失败: {e}")

    try:
        redis_config = {
            'host': config.get('REDIS_HOST', 'localhost'),
            'port': config.get('REDIS_PORT', 6379),
            'db': config.get('REDIS_DB', 0),
            'password': config.get('REDIS_PASSWORD', None)
        }
        redis_helper = RedisHelper(**redis_config)
        logger.info("RedisHelper 初始化成功。")
    except RedisConnectionError as e:
        logger.error(f"启动时连接 Redis 失败: {e}。主调度器终止。")
        return
    except Exception as e:
        logger.error(f"初始化 RedisHelper 时发生意外错误: {e}。主调度器终止。")
        return

    # 不再使用全局Playwright实例，避免事件循环冲突
    # 每个任务将创建独立的Playwright实例
    playwright_loop = None
    playwright_instance = None
    logger.info("配置为每个任务创建独立的Playwright实例，避免事件循环冲突")

    # MAX_DAILY_TASKS 相关状态变量
    current_day_max_tasks: Optional[int] = None # 初始化为 None，表示尚未从 Redis 获取
    last_max_daily_tasks_fetch_date: Optional[datetime.date] = None
    next_redis_fetch_attempt_time: datetime.datetime = datetime.datetime.now() # 立即尝试获取
    
    # 新增：聊天交互计划相关变量
    task_chat_plan_today: List[bool] = []
    tasks_scheduled_today_count = 0 # 当天已按计划分配的任务数 (用于从 task_chat_plan_today 取值)
    # --- 结束新增 ---

    # 尝试在启动时获取一次动态任务数用于线程计算
    initial_tasks_for_threads = 0
    try:
        logger.info("尝试在启动时从 Redis 获取动态 MAX_DAILY_TASKS...")
        initial_fetched_tasks = _get_dynamic_max_tasks_from_redis(redis_helper, config)
        if initial_fetched_tasks <= 0:
            logger.warning(f"启动时从 Redis 获取 MAX_DAILY_TASKS 失败或为0。将使用配置文件中的值进行初始线程计算。")
            config_fallback_tasks = config.get('MAX_DAILY_TASKS', 10) # 使用配置值或默认值
            if config_fallback_tasks <= 0:
                logger.warning(f"配置文件中的 MAX_DAILY_TASKS ({config_fallback_tasks}) 也无效，为初始线程计算设置一个保底任务数 1。")
                initial_tasks_for_threads = 1
            else:
                initial_tasks_for_threads = config_fallback_tasks
                logger.info(f"使用配置文件中的 MAX_DAILY_TASKS ({initial_tasks_for_threads}) 进行初始线程计算。")
        else:
            initial_tasks_for_threads = initial_fetched_tasks
            logger.info(f"使用从 Redis 获取的动态 MAX_DAILY_TASKS ({initial_tasks_for_threads}) 进行初始线程计算。")
            # 如果启动时成功获取，也更新 current_day_max_tasks 和相关时间戳
            current_day_max_tasks = initial_tasks_for_threads
            last_max_daily_tasks_fetch_date = datetime.datetime.now().date()
            next_redis_fetch_attempt_time = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0) + datetime.timedelta(days=1)
            # **** MODIFIED: 启动时生成聊天计划，传递日期 ****
            task_chat_plan_today, tasks_scheduled_today_count = _update_task_chat_plan(config, current_day_max_tasks, logger, redis_helper, config.get('REDIS_TASK_NAME_PREFIX', 'chat_task'), last_max_daily_tasks_fetch_date)

    except RedisConnectionError as rce_init:
        logger.error(f"启动时连接 Redis 获取 MAX_DAILY_TASKS 失败: {rce_init}。将使用配置文件进行初始线程计算。")
        config_fallback_tasks = config.get('MAX_DAILY_TASKS', 10)
        if config_fallback_tasks <= 0:
            logger.warning(f"配置文件中的 MAX_DAILY_TASKS ({config_fallback_tasks}) 无效，保底任务数 1。")
            initial_tasks_for_threads = 1
        else:
            initial_tasks_for_threads = config_fallback_tasks
            logger.info(f"使用配置文件中的 MAX_DAILY_TASKS ({initial_tasks_for_threads}) 进行初始线程计算。")
        # **** MODIFIED: 启动时生成聊天计划，传递日期 ****
        if current_day_max_tasks is None: # 如果上面try块中没设置成功
            current_day_max_tasks = initial_tasks_for_threads 
        current_date_for_plan = datetime.datetime.now().date()
        task_chat_plan_today, tasks_scheduled_today_count = _update_task_chat_plan(config, current_day_max_tasks, logger, redis_helper, config.get('REDIS_TASK_NAME_PREFIX', 'chat_task'), current_date_for_plan)
        # 即使启动时获取动态配置失败，也用当前日期标记计划的基础日期
        last_max_daily_tasks_fetch_date = current_date_for_plan 
        next_redis_fetch_attempt_time = datetime.datetime.now() + datetime.timedelta(minutes=10) # 10分钟后重试

    except Exception as e_init:
        logger.error(f"启动时获取 MAX_DAILY_TASKS 发生未知错误: {e_init}。将使用配置文件进行初始线程计算。", exc_info=True)
        config_fallback_tasks = config.get('MAX_DAILY_TASKS', 10)
        if config_fallback_tasks <= 0:
             logger.warning(f"配置文件中的 MAX_DAILY_TASKS ({config_fallback_tasks}) 无效，保底任务数 1。")
             initial_tasks_for_threads = 1
        else:
            initial_tasks_for_threads = config_fallback_tasks
            logger.info(f"使用配置文件中的 MAX_DAILY_TASKS ({initial_tasks_for_threads}) 进行初始线程计算。")
        next_redis_fetch_attempt_time = datetime.datetime.now() + datetime.timedelta(minutes=10) # 10分钟后重试
        # **** MODIFIED: 启动时生成聊天计划，传递日期 ****
        if current_day_max_tasks is None: # 如果上面try块中没设置成功
            current_day_max_tasks = initial_tasks_for_threads 
        current_date_for_plan = datetime.datetime.now().date()
        task_chat_plan_today, tasks_scheduled_today_count = _update_task_chat_plan(config, current_day_max_tasks, logger, redis_helper, config.get('REDIS_TASK_NAME_PREFIX', 'chat_task'), current_date_for_plan)
        # 即使启动时获取动态配置失败，也用当前日期标记计划的基础日期
        last_max_daily_tasks_fetch_date = current_date_for_plan
        next_redis_fetch_attempt_time = datetime.datetime.now() + datetime.timedelta(minutes=10) # 10分钟后重试

    # 使用获取到的或后备的任务数计算线程
    num_threads = calculate_optimal_threads(config, initial_tasks_for_threads)
    executor = concurrent.futures.ThreadPoolExecutor(max_workers=num_threads)
    logger.info(f"ThreadPoolExecutor 初始化成功，工作线程数: {num_threads} (基于动态或后备配置计算)。")

    active_futures = [] # 用于跟踪已提交但未完成的任务的Future对象
    global_task_counter = 0
    task_name_prefix = config.get('REDIS_TASK_NAME_PREFIX', 'chat_task')
    min_sleep_interval = config.get('MIN_SCHEDULER_SLEEP_SECONDS', 10) # 调度器最小休眠时间

    try:
        while not shutdown_event.is_set():
            logger.info("主调度循环：新一轮检查开始。")
            now = datetime.datetime.now()
            
            # 尝试从 Redis 获取和计算 MAX_DAILY_TASKS (现在改为调用辅助函数)
            if now >= next_redis_fetch_attempt_time and \
               (last_max_daily_tasks_fetch_date is None or last_max_daily_tasks_fetch_date != now.date()): # MODIFIED: Added None check for safety
                logger.info(f"尝试从 Redis 刷新动态 MAX_DAILY_TASKS 配置... (上次计划日期: {last_max_daily_tasks_fetch_date.isoformat() if last_max_daily_tasks_fetch_date else 'N/A'}, 当前日期: {now.date().isoformat()})")
                previous_max_tasks = current_day_max_tasks 
                try:
                    temp_max_tasks = _get_dynamic_max_tasks_from_redis(redis_helper, config)
                    current_day_max_tasks = temp_max_tasks
                    current_processing_date = now.date() # Date for which these tasks and plan apply

                    # 如果 MAX_DAILY_TASKS 更新了，或者日期变化了，重新生成聊天计划
                    if previous_max_tasks != current_day_max_tasks or \
                       (last_max_daily_tasks_fetch_date is None or last_max_daily_tasks_fetch_date != current_processing_date):
                        logger.info(f"检测到总任务数变化 ({previous_max_tasks} -> {current_day_max_tasks}) 或日期变化。将为日期 {current_processing_date.isoformat()} 更新聊天计划。")
                        task_chat_plan_today, tasks_scheduled_today_count = _update_task_chat_plan(config, current_day_max_tasks, logger, redis_helper, task_name_prefix, current_processing_date)
                        last_max_daily_tasks_fetch_date = current_processing_date # 更新计划所对应的日期
                    else:
                        logger.info(f"MAX_DAILY_TASKS未变化 ({current_day_max_tasks}) 且日期 ({current_processing_date.isoformat()}) 未变，无需更新聊天计划。")
                    
                    next_redis_fetch_attempt_time = now.replace(hour=0, minute=0, second=0, microsecond=0) + datetime.timedelta(days=1)

                except RedisConnectionError as rce:
                    logger.error(f"刷新 MAX_DAILY_TASKS 时 Redis 连接失败: {rce}。将按10分钟后重试。")
                    # 连接失败时，保持 current_day_max_tasks 不变或设为0？这里设为0更安全
                    current_day_max_tasks = 0
                    next_redis_fetch_attempt_time = now + datetime.timedelta(minutes=10)
                except Exception as ex_redis:
                    # 捕获 _get_dynamic_max_tasks_from_redis 可能未处理的其他异常
                    logger.error(f"刷新 MAX_DAILY_TASKS 时发生未知错误: {ex_redis}。将按10分钟后重试。", exc_info=True)
                    current_day_max_tasks = 0 # 未知错误，设为0
                    next_redis_fetch_attempt_time = now + datetime.timedelta(minutes=10)

            # 确保 current_day_max_tasks 有一个值 (至少是 0)
            if current_day_max_tasks is None: # 应该只在极少见情况下发生（例如启动和循环首次检查都失败且未设后备）
                logger.warning("current_day_max_tasks 仍然为 None，强制设为 0。")
                current_day_max_tasks = 0
            
            effective_max_daily_tasks = current_day_max_tasks

            logger.info(f"主调度循环：检查点。当日有效最大任务数 (MAX_DAILY_TASKS): {effective_max_daily_tasks}")
            current_hour = now.hour

            start_hour = config.get('START_HOUR', 0)
            end_hour = config.get('END_HOUR', 23) # 从配置获取的实际值（例如：0）或默认值（23）

            logger.debug(f"当前时间: {now}, 当前小时: {current_hour}, 配置的开始时间: {start_hour}, 配置的结束时间(来自config或默认): {end_hour}")

            # 1. 检查是否在任务执行时间段内
            is_currently_active = False
            if start_hour <= end_hour:
                # 标准时间窗口，例如：START_HOUR: 9, END_HOUR: 17（活动时间 9:00-16:59）
                # 或者，例如：START_HOUR: 0, END_HOUR: 0（如果配置为 END_HOUR:0 和 START_HOUR:0，表示 00:00-23:59，所以当 0 <= current_hour < 0 时活动，这是错误的。如果 start_hour=0, end_hour=0 表示全天运行，这个边界情况需要特别处理）
                # 但是，如果配置的 END_HOUR 是 0，且 START_HOUR > 0，它会进入 'else' 分支
                # 如果 START_HOUR 是 0 且配置的 END_HOUR 是 0，表示活动时间 00:00-23:59:59
                if start_hour == 0 and end_hour == 0: # 如果配置为 START_HOUR:0, END_HOUR:0 时的全天运行特殊情况
                    is_currently_active = True # 始终活动
                elif start_hour <= current_hour < end_hour:
                    is_currently_active = True
            else: # start_hour > end_hour。时间窗口跨越午夜 或 是 START_HOUR:X, END_HOUR:0（表示 X:00 到 23:59:59）
                  # 示例：START_HOUR: 22, END_HOUR: 2（当 current_hour >= 22 或 current_hour < 2 时活动）
                  # 示例：START_HOUR: 4, END_HOUR: 0（当 current_hour >= 4 或 current_hour < 0 时活动 -> 实际上就是 current_hour >= 4）
                if current_hour >= start_hour or current_hour < end_hour:
                    is_currently_active = True
            
            if not is_currently_active:
                display_window_end_str = ""
                if end_hour == 0: # 配置为 0，表示到 23:59:59
                    display_window_end_str = "23:59"
                else: # 标准情况，例如：end_hour 17 表示到 16:59
                    display_window_end_str = f"{end_hour-1}:59"
                
                logger.info(f"不在活动时间 ({start_hour}:00 - {display_window_end_str})。当前小时: {current_hour}。")
                
                # 确定下一个开始时间是今天还是明天
                aim_for_today_start = False
                if start_hour <= end_hour: # 标准时间窗口（例如：9-17）
                    if current_hour < start_hour: # 当前是 8，开始是 9 -> 目标是今天的 9:00
                        aim_for_today_start = True
                    # 否则（current_hour >= end_hour），例如：当前是 17 -> 目标是明天的 9:00（默认 aim_for_today_start = False）
                else: # 时间窗口跨越午夜（例如：22-02）或是类似 4-0（表示 4:00-23:59:59）
                      # 如果不活动，意味着 current_hour 在"间隙"中（current_hour >= end_hour 且 current_hour < start_hour）
                      # 示例：start=4, end=0。不活动意味着 current_hour 在 [0,1,2,3] 中。目标是今天的 4:00。
                      # 示例：start=22, end=2。不活动意味着 current_hour 在 [2,3,...,21] 中。目标是今天的 22:00。
                    if current_hour >= end_hour and current_hour < start_hour:
                         aim_for_today_start = True
                    # 如果是跨越午夜的时间窗口，且 current_hour 不在这个特定的间隙中但仍然不活动，
                    # 这可能意味着边界情况或 is_currently_active 的逻辑需要完善。
                    # 对于 4-0，如果 current_hour = 3，不活动。current_hour(3) >= end_hour(0) 且 current_hour(3) < start_hour(4)。所以 aim_for_today_start = True。

                next_start_time_dt: datetime.datetime
                target_date = now.date()
                if not aim_for_today_start:
                    target_date = now.date() + datetime.timedelta(days=1)
                
                next_start_time_dt = datetime.datetime(target_date.year, target_date.month, target_date.day, start_hour, 0, 0)

                if aim_for_today_start:
                    logger.debug(f"当前小时 {current_hour} 在今日任务时段开始（{start_hour}:00）之前，或在跨天任务的非活跃间隙。目标为今日 {start_hour}:00。")
                else:
                    logger.debug(f"今日任务时段 ({start_hour}:00 - {display_window_end_str}) 已结束。目标为明日 {start_hour}:00。")
                logger.debug(f"计算得到的下一个开始时间: {next_start_time_dt}")
                
                sleep_seconds = (next_start_time_dt - now).total_seconds()
                sleep_seconds = max(min_sleep_interval, sleep_seconds) # 确保至少休眠min_sleep_interval
                logger.info(f"调度器休眠约 {sleep_seconds/3600:.2f} 小时，直到下一个活动窗口。实际休眠秒数: {sleep_seconds}")
                
                # 使用可中断的休眠
                start_actual_sleep = time.monotonic()
                while time.monotonic() - start_actual_sleep < sleep_seconds:
                    if shutdown_event.is_set():
                        logger.info("休眠期间收到关闭信号，中断休眠。")
                        break
                    time.sleep(1) # 每秒检查一次关闭信号
                if shutdown_event.is_set(): break
                logger.info("长时间休眠结束。")
                continue

            # 2. 清理已完成的 futures
            active_futures = [f for f in active_futures if not f.done()] # 保留未完成的

            # 3. 检查当日任务是否已达上限 (结合已完成和正在处理的)
            try:
                tasks_done_today = redis_helper.get_daily_task_count(task_name_prefix)
            except RedisConnectionError as rce_get:
                logger.error(f"获取任务计数时 Redis 连接错误: {rce_get}。将在 {min_sleep_interval} 秒后重试。")
                time.sleep(min_sleep_interval)
                if shutdown_event.is_set(): break
                continue
            
            current_pending_tasks = len(active_futures)

            logger.debug(f"今日已完成任务 (Redis): {tasks_done_today}, 当前已提交未完成任务: {current_pending_tasks}, 当日有效最大任务数: {effective_max_daily_tasks}")

            if effective_max_daily_tasks == 0:
                logger.info(f"当日有效最大任务数为 0。不提交新任务。下次 Redis 配置检查时间: {next_redis_fetch_attempt_time.strftime('%H:%M:%S') if next_redis_fetch_attempt_time > now else '立即或按计划'}")
                # 即使任务数为0，也需要休眠，避免空转消耗CPU
                start_sleep_for_zero_tasks = time.monotonic()
                # 休眠时间可以是 min_sleep_interval，或者到 next_redis_fetch_attempt_time 的较小者
                sleep_duration_for_zero = min_sleep_interval
                if next_redis_fetch_attempt_time > now :
                    sleep_duration_for_zero = min(min_sleep_interval, (next_redis_fetch_attempt_time - now).total_seconds())
                    sleep_duration_for_zero = max(1, sleep_duration_for_zero) # 至少休眠1秒
                
                logger.info(f"因当日任务数为0，调度器休眠 {sleep_duration_for_zero:.0f} 秒。")
                while time.monotonic() - start_sleep_for_zero_tasks < sleep_duration_for_zero:
                    if shutdown_event.is_set(): break
                    time.sleep(0.1)
                if shutdown_event.is_set(): break
                continue

            if tasks_done_today >= effective_max_daily_tasks:
                logger.info(f"已达到每日任务上限 ({tasks_done_today}/{effective_max_daily_tasks}) 基于Redis计数和动态配置。")
                tomorrow_start = datetime.datetime(now.year, now.month, now.day, start_hour, 0, 0) + datetime.timedelta(days=1)
                sleep_duration = (tomorrow_start - now).total_seconds()
                sleep_duration = max(min_sleep_interval, sleep_duration)
                logger.info(f"休眠 {sleep_duration/3600:.2f} 小时直到次日。实际休眠秒数: {sleep_duration}")
                
                start_actual_sleep_daily_limit = time.monotonic()
                while time.monotonic() - start_actual_sleep_daily_limit < sleep_duration:
                    if shutdown_event.is_set():
                        logger.info("因每日上限休眠期间收到关闭信号，中断休眠。")
                        break
                    time.sleep(1)
                if shutdown_event.is_set(): break
                logger.info("因每日上限导致的休眠结束。")
                continue
            elif (tasks_done_today + current_pending_tasks) >= effective_max_daily_tasks:
                logger.info(f"已完成 {tasks_done_today} + 正在处理 {current_pending_tasks} = {tasks_done_today + current_pending_tasks} 任务，已达到或超过每日上限 {effective_max_daily_tasks}。等待现有任务完成。")
                # 短暂休眠，等待已提交的任务完成
                start_short_sleep = time.monotonic()
                while time.monotonic() - start_short_sleep < min_sleep_interval: # 使用min_sleep_interval作为等待周期
                    if shutdown_event.is_set():
                        break
                    time.sleep(0.1)
                if shutdown_event.is_set(): break
                continue

            # 4. 计算任务执行间隔以分散任务 (现在可以安全地提交一个任务)
            # remaining_tasks_for_completion 代表还需要多少任务成功完成
            remaining_tasks_for_completion = max(0, effective_max_daily_tasks - tasks_done_today)
            
            # 确定当天活动窗口的实际结束时间点，用于计算剩余秒数
            # 'end_hour' (来自 line 165) 是从 config.yaml 读取的 END_HOUR 值
            if end_hour == 0: 
                # 配置为0意味着当天运行到23:59:59。为了计算剩余时间，我们将窗口视为到次日0点结束。
                effective_window_end_dt = (now.replace(hour=0, minute=0, second=0, microsecond=0) + 
                                           datetime.timedelta(days=1))
            else:
                # 例如 END_HOUR 为 17, 意味着运行到 16:59:59。窗口在当天的 17:00:00 结束。
                effective_window_end_dt = now.replace(hour=end_hour, minute=0, second=0, microsecond=0)
            
            remaining_seconds_in_window = max(0, (effective_window_end_dt - now).total_seconds())
            logger.debug(f"Configured END_HOUR: {end_hour}, Effective window end: {effective_window_end_dt}, Remaining seconds in window: {remaining_seconds_in_window:.2f}")

            sleep_interval = min_sleep_interval # 默认最小间隔
            if remaining_tasks_for_completion > 0 and remaining_seconds_in_window > 0:
                avg_interval = remaining_seconds_in_window / remaining_tasks_for_completion
                # 引入随机性：平均间隔的 0.5到1.5倍，但不小于min_sleep_interval，也不大于一个最大值(例如ESTIMATED_SINGLE_TASK_DURATION_SECONDS)
                estimated_duration = config.get('ESTIMATED_SINGLE_TASK_DURATION_SECONDS', 300)
                random_factor_min = config.get('TASK_INTERVAL_RANDOM_FACTOR_MIN', 0.7)
                random_factor_max = config.get('TASK_INTERVAL_RANDOM_FACTOR_MAX', 1.3)
                
                lower_bound = max(min_sleep_interval, avg_interval * random_factor_min)
                upper_bound = min(estimated_duration, avg_interval * random_factor_max) # 避免间隔过长
                if lower_bound >= upper_bound: # 防止随机范围无效
                    sleep_interval = lower_bound
                else:
                    sleep_interval = random.uniform(lower_bound, upper_bound)
            
            next_submission_dt = now + datetime.timedelta(seconds=sleep_interval)
            logger.info(f"已完成任务: {tasks_done_today}/{effective_max_daily_tasks}。窗口剩余约: {remaining_seconds_in_window/3600:.1f}h。下一个任务计划在【{next_submission_dt.strftime('%H:%M:%S')}】 (约 {sleep_interval:.0f}s 后)提交。")
            
            # 5. 提交任务到线程池
            global_task_counter += 1
            
            # 从聊天计划中决定当前任务是否进行聊天
            should_perform_chat_for_this_task = False # 默认不聊天
            if tasks_scheduled_today_count < len(task_chat_plan_today):
                should_perform_chat_for_this_task = task_chat_plan_today[tasks_scheduled_today_count]
            elif effective_max_daily_tasks > 0 : # 如果计划用完了但理论上还有任务额度，则默认不聊天
                 logger.warning(f"[任务 {global_task_counter}] 聊天交互计划已用尽 ({tasks_scheduled_today_count}/{len(task_chat_plan_today)})，但仍有任务额度。此任务将不进行聊天。")


            logger.info(f"提交任务 {global_task_counter} 到线程池。是否执行聊天: {should_perform_chat_for_this_task}。(今日已完成: {tasks_done_today}, 正在处理: {current_pending_tasks}, 目标: {effective_max_daily_tasks}, 当日计划内任务计数: {tasks_scheduled_today_count+1}/{len(task_chat_plan_today) if task_chat_plan_today else 'N/A'})")
            future = executor.submit(worker_task_wrapper, playwright_instance, config, global_task_counter, redis_helper, effective_max_daily_tasks, should_perform_chat_for_this_task)
            active_futures.append(future)
            tasks_scheduled_today_count += 1 # 增加已按计划分配的任务计数
            
            # 6. 等待下一个任务的调度点
            # 这里使用 time.sleep，允许信号处理中断它
            start_sleep_for_next_task = time.monotonic()
            while time.monotonic() - start_sleep_for_next_task < sleep_interval:
                if shutdown_event.is_set():
                    break
                time.sleep(0.1) # 短暂睡眠，以便快速响应shutdown_event
            if shutdown_event.is_set(): break

    except KeyboardInterrupt:
        logger.warning("收到 KeyboardInterrupt。开始关闭...")
        shutdown_event.set()
    except Exception as e:
        logger.critical(f"主调度器发生严重错误: {e}", exc_info=True)
        shutdown_event.set() # 发生未知严重错误也尝试关闭
    finally:
        logger.info("正在关闭 ThreadPoolExecutor...")
        # 等待所有已提交任务完成，但不接受新任务
        # 如果希望立即停止，可以设置 cancel_futures=True (Python 3.9+)
        executor.shutdown(wait=True)
        logger.info("ThreadPoolExecutor 已关闭。")
        
        # 停止系统清理守护进程
        if system_cleanup:
            try:
                system_cleanup.stop_cleanup_daemon()
            except Exception as e:
                logger.error(f"停止系统清理守护进程时出错: {e}")

        # 停止健康监控
        if health_monitor:
            try:
                health_monitor.stop_monitoring()
                logger.info("自动健康监控已停止。")
            except Exception as e:
                logger.error(f"停止自动健康监控时出错: {e}")
        
        # 不再需要停止全局Playwright实例，因为每个任务使用独立实例
        logger.info("所有任务已使用独立的Playwright实例，无需停止全局实例")

        # 如果发生了严重的浏览器启动错误，执行强制清理
        logger.info("执行最终系统清理...")
        logger.info("主调度器已结束。")

if __name__ == "__main__":
    # 注册信号处理器
    signal.signal(signal.SIGINT, handle_signal)  # Ctrl+C
    signal.signal(signal.SIGTERM, handle_signal) # kill 命令

    logger.info("启动聊天机器人自动化工具...")
    main_scheduler()
    logger.info("聊天机器人自动化工具已关闭。")
