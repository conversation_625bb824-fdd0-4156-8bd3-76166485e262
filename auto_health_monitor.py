#!/usr/bin/env python3
"""
自动健康监控和自愈系统
确保程序能够自动稳定运行，无需手动干预
"""

import time
import threading
import logging
from datetime import datetime, timedelta
from utils.logger import logger
from utils.system_cleanup import force_system_resource_cleanup

class AutoHealthMonitor:
    """自动健康监控和自愈系统"""
    
    def __init__(self, config):
        self.config = config
        self.running = False
        self.monitor_thread = None
        
        # 监控配置
        self.check_interval = config.get('HEALTH_CHECK_INTERVAL_MINUTES', 5) * 60  # 转换为秒
        self.max_chrome_processes = config.get('MAX_CHROME_PROCESSES', 2)
        self.max_memory_percent = config.get('MAX_SYSTEM_MEMORY_PERCENT', 75)
        self.max_open_files = config.get('MAX_OPEN_FILES_PER_PROCESS', 200)
        self.max_chrome_runtime_minutes = config.get('MAX_CHROME_RUNTIME_MINUTES', 30)
        
        # 自愈配置
        self.auto_healing_enabled = config.get('AUTO_HEALING_ENABLED', True)
        self.emergency_cleanup_threshold = config.get('EMERGENCY_CLEANUP_THRESHOLD', 3)  # 连续3次检查异常触发紧急清理
        
        # 状态跟踪
        self.consecutive_issues = 0
        self.last_cleanup_time = datetime.now()
        self.health_stats = {
            'total_checks': 0,
            'issues_detected': 0,
            'auto_cleanups': 0,
            'emergency_cleanups': 0
        }
    
    def start_monitoring(self):
        """启动健康监控"""
        if self.running:
            logger.warning("健康监控已在运行中")
            return
            
        self.running = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        logger.info(f"自动健康监控已启动，检查间隔: {self.check_interval/60:.1f}分钟")
    
    def stop_monitoring(self):
        """停止健康监控"""
        self.running = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logger.info("自动健康监控已停止")
    
    def _monitor_loop(self):
        """监控主循环"""
        while self.running:
            try:
                self._perform_health_check()
                time.sleep(self.check_interval)
            except Exception as e:
                logger.error(f"健康监控过程中出错: {e}")
                time.sleep(60)  # 出错时等待1分钟再继续
    
    def _perform_health_check(self):
        """执行健康检查"""
        self.health_stats['total_checks'] += 1
        issues = []
        
        try:
            import psutil
            
            # 1. 检查Chrome进程数量
            chrome_count = self._count_chrome_processes()
            if chrome_count > self.max_chrome_processes:
                issues.append(f"Chrome进程过多: {chrome_count}>{self.max_chrome_processes}")
            
            # 2. 检查内存使用率
            memory_percent = psutil.virtual_memory().percent
            if memory_percent > self.max_memory_percent:
                issues.append(f"内存使用率过高: {memory_percent:.1f}%>{self.max_memory_percent}%")
            
            # 3. 检查文件描述符
            try:
                current_process = psutil.Process()
                open_files = len(current_process.open_files())
                if open_files > self.max_open_files:
                    issues.append(f"打开文件过多: {open_files}>{self.max_open_files}")
            except Exception:
                pass
            
            # 4. 检查长时间运行的Chrome进程
            long_running_chrome = self._find_long_running_chrome()
            if long_running_chrome:
                issues.append(f"发现长时间运行的Chrome进程: {len(long_running_chrome)}个")
            
            # 处理检查结果
            if issues:
                self._handle_health_issues(issues)
            else:
                self._handle_healthy_state()
                
        except ImportError:
            logger.warning("psutil未安装，跳过详细健康检查")
        except Exception as e:
            logger.error(f"健康检查时出错: {e}")
    
    def _count_chrome_processes(self):
        """统计Chrome进程数量"""
        try:
            import psutil
            chrome_count = 0
            for proc in psutil.process_iter(['name']):
                try:
                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                        chrome_count += 1
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            return chrome_count
        except Exception:
            return 0
    
    def _find_long_running_chrome(self):
        """查找长时间运行的Chrome进程"""
        long_running = []
        try:
            import psutil
            import time
            
            current_time = time.time()
            for proc in psutil.process_iter(['pid', 'name', 'create_time']):
                try:
                    proc_info = proc.info
                    if proc_info['name'] and 'chrome' in proc_info['name'].lower():
                        runtime_minutes = (current_time - proc_info['create_time']) / 60
                        if runtime_minutes > self.max_chrome_runtime_minutes:
                            long_running.append({
                                'pid': proc_info['pid'],
                                'runtime_minutes': runtime_minutes
                            })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception:
            pass
        return long_running
    
    def _handle_health_issues(self, issues):
        """处理健康问题"""
        self.health_stats['issues_detected'] += 1
        self.consecutive_issues += 1
        
        logger.warning(f"健康检查发现问题 (连续第{self.consecutive_issues}次): {'; '.join(issues)}")
        
        if self.auto_healing_enabled:
            # 执行自动清理
            self._perform_auto_cleanup()
            
            # 如果连续多次出现问题，执行紧急清理
            if self.consecutive_issues >= self.emergency_cleanup_threshold:
                self._perform_emergency_cleanup()
    
    def _handle_healthy_state(self):
        """处理健康状态"""
        if self.consecutive_issues > 0:
            logger.info(f"系统状态已恢复正常 (之前连续{self.consecutive_issues}次异常)")
        
        self.consecutive_issues = 0
        
        # 定期记录健康状态
        if self.health_stats['total_checks'] % 12 == 0:  # 每12次检查记录一次
            self._log_health_stats()
    
    def _perform_auto_cleanup(self):
        """执行自动清理"""
        try:
            logger.info("执行自动资源清理...")
            cleaned_count = force_system_resource_cleanup()
            self.health_stats['auto_cleanups'] += 1
            self.last_cleanup_time = datetime.now()
            logger.info(f"自动清理完成，清理了 {cleaned_count} 个项目")
        except Exception as e:
            logger.error(f"自动清理时出错: {e}")
    
    def _perform_emergency_cleanup(self):
        """执行紧急清理"""
        try:
            logger.warning(f"连续{self.consecutive_issues}次检查异常，执行紧急清理...")
            
            # 强制清理所有Chrome进程
            import subprocess
            try:
                subprocess.run(['pkill', '-9', '-f', 'chrome'], 
                             capture_output=True, timeout=10)
                logger.info("紧急清理: 已强制终止所有Chrome进程")
            except Exception as e:
                logger.error(f"紧急清理Chrome进程失败: {e}")
            
            # 执行系统资源清理
            self._perform_auto_cleanup()
            
            self.health_stats['emergency_cleanups'] += 1
            self.consecutive_issues = 0  # 重置计数器
            
            logger.warning("紧急清理完成")
            
        except Exception as e:
            logger.error(f"紧急清理时出错: {e}")
    
    def _log_health_stats(self):
        """记录健康统计信息"""
        stats = self.health_stats
        uptime_hours = (datetime.now() - (datetime.now() - timedelta(seconds=stats['total_checks'] * self.check_interval / 60))).total_seconds() / 3600
        
        logger.info(f"健康监控统计 - 运行时间: {uptime_hours:.1f}小时, "
                   f"总检查: {stats['total_checks']}, "
                   f"发现问题: {stats['issues_detected']}, "
                   f"自动清理: {stats['auto_cleanups']}, "
                   f"紧急清理: {stats['emergency_cleanups']}")
    
    def get_health_status(self):
        """获取健康状态"""
        return {
            'running': self.running,
            'consecutive_issues': self.consecutive_issues,
            'last_cleanup_time': self.last_cleanup_time,
            'stats': self.health_stats.copy()
        }
