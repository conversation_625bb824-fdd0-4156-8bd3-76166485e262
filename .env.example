# .env.example - Environment variables for chatbot_automation
# Copy this file to .env and fill in your actual values.
# .env file should be added to .gitignore if it contains sensitive information.

# --- Critical API Keys (Required) ---
OPENAI_API_KEY="YOUR_OPENAI_API_KEY_HERE"
PROXY_API_KEY="YOUR_PROXY_API_KEY_HERE"
DYNAMIC_URL_API_KEY="YOUR_DYNAMIC_URL_API_KEY_HERE" # Example: zk789012

# --- API Endpoints (Modify if different from config.yaml defaults) ---
# PROXY_API_URL="https://www.lthttp.com/iplist"
# DYNAMIC_URL_API_ENDPOINT="http://www.zhikaobibei.com/api3/get-article-url"
# OPENAI_BASE_URL="https://ark.cn-beijing.volces.com/api/v3" # Uncomment and set if using a custom OpenAI endpoint

# --- Redis Configuration (Required for operation) ---
REDIS_HOST="localhost" # Or your Redis server IP/hostname
REDIS_PORT="6379"
REDIS_DB="0"
# REDIS_PASSWORD="YOUR_REDIS_PASSWORD_IF_ANY"

# --- Task Control & Scheduling (Optional overrides) ---
# MAX_DAILY_TASKS="50" # Overrides config.yaml, but dynamic Redis config for this takes precedence if available
# START_HOUR="4"       # Example: tasks start at 4 AM
# END_HOUR="0"         # Example: tasks run until 23:59:59 (0 means end of day)
# LOG_LEVEL="INFO"     # DEBUG, INFO, WARNING, ERROR, CRITICAL
HEADLESS_BROWSER="true" # true for server, false for local debugging

# --- Dynamic MAX_DAILY_TASKS Redis config keys (Override if different from config.yaml defaults) ---
# REDIS_TASK_NAME_PREFIX="chat_task"
# REDIS_DYNAMIC_CONFIG_HASH_KEY="form_submit_config"
# REDIS_FIELD_MAX_SUBMISSIONS="max_agent_submissions"
# REDIS_FIELD_SUBMISSION_RATE="agent_submission_rate"

# --- Other settings ---
# USER_AGENT_TYPE="pc" # pc, mobile, random etc.
# MAX_CHAT_INTERACTIONS="3"
# ESTIMATED_SINGLE_TASK_DURATION_SECONDS="200"