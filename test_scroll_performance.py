#!/usr/bin/env python3
"""
滚动性能测试 - 验证优化后的滚动操作性能
"""

import asyncio
import time
from task_runner import TaskRunner
from utils.logger import logger
import yaml

async def test_scroll_performance():
    """测试滚动操作的性能"""
    logger.info("开始滚动性能测试...")
    
    # 加载配置
    try:
        with open('config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        logger.info("配置加载成功")
    except Exception as e:
        logger.error(f"配置加载失败: {e}")
        return False
    
    # 修改配置以跳过代理
    config['PROXY_API_URL'] = ""  # 禁用代理进行测试
    config['PROXY_API_KEY'] = ""
    
    # 创建测试任务
    task_runner = TaskRunner(config=config, task_id=888, should_perform_chat_interaction=False)
    
    try:
        start_time = time.time()
        
        # 1. 初始化浏览器
        await task_runner._initialize_browser()
        init_time = time.time()
        logger.info(f"✅ 浏览器初始化完成，耗时: {init_time - start_time:.2f}秒")
        
        # 2. 获取目标URL
        target_url = await task_runner._get_target_url()
        if not target_url:
            logger.error("❌ 无法获取目标URL")
            return False
        
        url_time = time.time()
        logger.info(f"✅ URL获取完成，耗时: {url_time - init_time:.2f}秒")
        
        # 3. 导航到页面
        await task_runner.page.goto(target_url, timeout=30000, wait_until='domcontentloaded')
        nav_time = time.time()
        logger.info(f"✅ 页面导航完成，耗时: {nav_time - url_time:.2f}秒")
        
        # 4. 测试滚动操作（关键测试）
        scroll_start = time.time()
        await task_runner._simulate_page_scroll()
        scroll_end = time.time()
        scroll_duration = scroll_end - scroll_start
        
        logger.info(f"🎯 滚动操作完成，耗时: {scroll_duration:.2f}秒")
        
        # 5. 总时间统计
        total_time = scroll_end - start_time
        logger.info(f"📊 总执行时间: {total_time:.2f}秒")
        
        # 性能评估
        if scroll_duration < 10:
            logger.info("🟢 滚动性能优秀 (< 10秒)")
            performance_grade = "优秀"
        elif scroll_duration < 30:
            logger.info("🟡 滚动性能良好 (10-30秒)")
            performance_grade = "良好"
        elif scroll_duration < 60:
            logger.info("🟠 滚动性能一般 (30-60秒)")
            performance_grade = "一般"
        else:
            logger.warning("🔴 滚动性能较差 (> 60秒)")
            performance_grade = "较差"
        
        # 预估完整任务时间
        estimated_total = total_time + 30  # 假设还需要30秒完成其他操作
        logger.info(f"📈 预估完整任务总时间: {estimated_total:.2f}秒")
        
        if estimated_total < 300:  # 小于5分钟
            logger.info("✅ 预估任务时间在超时限制内")
            return True
        else:
            logger.warning("⚠️ 预估任务时间可能超时，建议进一步优化")
            return False
            
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}", exc_info=True)
        return False
    finally:
        # 清理资源
        try:
            await task_runner._cleanup()
        except Exception as e:
            logger.error(f"清理资源失败: {e}")

async def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("滚动性能测试开始")
    logger.info("=" * 60)
    
    success = await test_scroll_performance()
    
    logger.info("=" * 60)
    if success:
        logger.info("🎉 滚动性能测试通过")
        print("\n✅ 测试结果：滚动性能优化成功，任务应该不会再超时")
    else:
        logger.warning("⚠️ 滚动性能测试发现问题")
        print("\n❌ 测试结果：滚动性能仍有问题，需要进一步优化")
    logger.info("=" * 60)

if __name__ == "__main__":
    asyncio.run(main()) 