#!/usr/bin/env python3
"""
自动重启脚本 - 监控chatbot_automation程序，在检测到关键错误时自动重启
使用方法: python restart_on_error.py
"""

import subprocess
import time
import os
import signal
import threading
from datetime import datetime, timedelta

class ProcessMonitor:
    """进程监控器"""
    
    def __init__(self):
        self.process = None
        self.running = False
        self.restart_count = 0
        self.max_restarts_per_hour = 10  # 每小时最大重启次数
        self.restart_times = []
        self.monitor_interval = 5  # 监控间隔（秒）
        
        # 需要监控的关键错误
        self.critical_errors = [
            'Failed to launch zygote process',
            'BrowserType.launch: Target page, context or browser has been closed',
            'Check failed: process.IsValid()',
            'FATAL:zygote_host_impl_linux.cc'
        ]
        
    def start_monitoring(self):
        """开始监控主程序"""
        self.running = True
        print(f"[{datetime.now()}] 启动监控器...")
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        while self.running:
            try:
                if not self._is_process_running():
                    print(f"[{datetime.now()}] 检测到主程序未运行，正在启动...")
                    self._start_main_process()
                else:
                    # 检查是否有错误日志
                    if self._check_for_critical_errors():
                        print(f"[{datetime.now()}] 检测到关键错误，正在重启...")
                        self._restart_process()
                
                time.sleep(self.monitor_interval)
                
            except KeyboardInterrupt:
                print(f"[{datetime.now()}] 收到中断信号，停止监控...")
                break
            except Exception as e:
                print(f"[{datetime.now()}] 监控过程中发生错误: {e}")
                time.sleep(10)
        
        self._cleanup()
        
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"[{datetime.now()}] 收到信号 {signum}，开始优雅关闭...")
        self.running = False
        
    def _is_process_running(self):
        """检查主进程是否在运行"""
        if self.process is None:
            return False
            
        poll_result = self.process.poll()
        return poll_result is None
        
    def _start_main_process(self):
        """启动主程序"""
        if not self._can_restart():
            print(f"[{datetime.now()}] 已达到每小时重启限制 ({self.max_restarts_per_hour})，等待中...")
            return False
            
        try:
            # 先执行预清理
            self._pre_restart_cleanup()
            
            # 启动主程序
            self.process = subprocess.Popen(
                ['python', 'main.py'],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            self._record_restart()
            print(f"[{datetime.now()}] 主程序已启动，PID: {self.process.pid}")
            
            # 启动日志监控线程
            log_thread = threading.Thread(target=self._monitor_logs, daemon=True)
            log_thread.start()
            
            return True
            
        except Exception as e:
            print(f"[{datetime.now()}] 启动主程序失败: {e}")
            return False
            
    def _monitor_logs(self):
        """监控程序输出日志"""
        if not self.process:
            return
            
        try:
            for line in iter(self.process.stdout.readline, ''):
                if not self.running:
                    break
                    
                # 打印日志（可选择性过滤）
                print(f"[MAIN] {line.rstrip()}")
                
                # 检查关键错误
                for error in self.critical_errors:
                    if error in line:
                        print(f"[{datetime.now()}] 检测到关键错误: {error}")
                        # 设置标记，由主监控循环处理重启
                        break
                        
        except Exception as e:
            print(f"[{datetime.now()}] 日志监控出错: {e}")
            
    def _check_for_critical_errors(self):
        """检查是否有关键错误（通过进程状态）"""
        if not self.process:
            return False
            
        # 如果进程意外退出
        if not self._is_process_running():
            returncode = self.process.returncode
            if returncode and returncode != 0:
                print(f"[{datetime.now()}] 主程序异常退出，返回码: {returncode}")
                return True
                
        return False
        
    def _restart_process(self):
        """重启进程"""
        if self.process:
            try:
                print(f"[{datetime.now()}] 正在终止进程 PID: {self.process.pid}")
                self.process.terminate()
                
                # 等待进程优雅退出
                try:
                    self.process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    print(f"[{datetime.now()}] 进程未在10秒内退出，强制终止...")
                    self.process.kill()
                    self.process.wait()
                    
            except Exception as e:
                print(f"[{datetime.now()}] 终止进程时出错: {e}")
                
        self.process = None
        time.sleep(3)  # 等待系统资源释放
        self._start_main_process()
        
    def _can_restart(self):
        """检查是否可以重启（限制频率）"""
        now = datetime.now()
        one_hour_ago = now - timedelta(hours=1)
        
        # 清理1小时前的重启记录
        self.restart_times = [t for t in self.restart_times if t > one_hour_ago]
        
        return len(self.restart_times) < self.max_restarts_per_hour
        
    def _record_restart(self):
        """记录重启时间"""
        self.restart_times.append(datetime.now())
        self.restart_count += 1
        
    def _pre_restart_cleanup(self):
        """重启前的清理工作"""
        try:
            # 清理僵尸chromium进程
            result = subprocess.run(['pgrep', '-f', 'chromium'], capture_output=True, text=True)
            if result.returncode == 0:
                pids = result.stdout.strip().split('\n')
                for pid_str in pids:
                    if pid_str.strip():
                        try:
                            pid = int(pid_str.strip())
                            os.kill(pid, signal.SIGTERM)
                            print(f"[{datetime.now()}] 清理进程 PID: {pid}")
                        except (ValueError, ProcessLookupError, PermissionError):
                            pass
                            
            # 清理临时文件
            temp_dirs = ['/tmp', '/var/tmp']
            for temp_dir in temp_dirs:
                if os.path.exists(temp_dir):
                    try:
                        for item in os.listdir(temp_dir):
                            if 'playwright' in item.lower() or 'chromium' in item.lower():
                                item_path = os.path.join(temp_dir, item)
                                if os.path.isdir(item_path):
                                    import shutil
                                    shutil.rmtree(item_path, ignore_errors=True)
                                else:
                                    os.remove(item_path)
                                print(f"[{datetime.now()}] 清理临时文件: {item_path}")
                    except Exception as e:
                        print(f"[{datetime.now()}] 清理 {temp_dir} 时出错: {e}")
                        
        except Exception as e:
            print(f"[{datetime.now()}] 预清理时出错: {e}")
            
    def _cleanup(self):
        """清理资源"""
        if self.process and self._is_process_running():
            try:
                print(f"[{datetime.now()}] 正在关闭主程序...")
                self.process.terminate()
                self.process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                self.process.kill()
                self.process.wait()
            except Exception as e:
                print(f"[{datetime.now()}] 清理时出错: {e}")
                
        print(f"[{datetime.now()}] 监控器已关闭，总重启次数: {self.restart_count}")

def main():
    """主函数"""
    print("ChatBot Automation 自动重启监控器")
    print("=" * 50)
    
    monitor = ProcessMonitor()
    monitor.start_monitoring()

if __name__ == "__main__":
    main() 