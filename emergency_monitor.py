#!/usr/bin/env python3
"""
紧急系统资源监控脚本
用于监控系统资源状态，在检测到严重问题时自动处理
"""

import os
import sys
import time
import signal
import subprocess
import logging
from datetime import datetime, timedelta

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('emergency_monitor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('emergency_monitor')

class EmergencyMonitor:
    def __init__(self):
        self.monitoring = True
        self.check_interval = 30  # 30秒检查一次
        self.emergency_restart_count = 0
        self.max_restarts_per_hour = 3
        self.restart_times = []
        
        # 阈值设置
        self.max_chrome_processes = 5
        self.max_memory_percent = 90
        self.max_threads_per_process = 100
        self.max_open_files_per_process = 400
        
    def start_monitoring(self):
        """开始监控"""
        logger.info("紧急系统资源监控启动")
        logger.info(f"监控间隔: {self.check_interval}秒")
        logger.info(f"阈值设置: Chrome进程<={self.max_chrome_processes}, 内存<={self.max_memory_percent}%, "
                   f"线程<={self.max_threads_per_process}, 文件<={self.max_open_files_per_process}")
        
        try:
            while self.monitoring:
                self._check_system_health()
                time.sleep(self.check_interval)
        except KeyboardInterrupt:
            logger.info("监控被用户中断")
        except Exception as e:
            logger.error(f"监控过程中发生错误: {e}", exc_info=True)
        finally:
            logger.info("紧急系统资源监控结束")
    
    def _check_system_health(self):
        """检查系统健康状况"""
        try:
            import psutil
            
            issues = []
            
            # 1. 检查Chrome进程数量
            chrome_processes = self._count_chrome_processes()
            if chrome_processes > self.max_chrome_processes:
                issues.append(f"Chrome进程过多: {chrome_processes}")
                
            # 2. 检查内存使用率
            memory_info = psutil.virtual_memory()
            if memory_info.percent > self.max_memory_percent:
                issues.append(f"内存使用率过高: {memory_info.percent:.1f}%")
                
            # 3. 检查主程序进程状态
            main_process = self._find_main_process()
            if main_process:
                # 检查线程数
                thread_count = main_process.num_threads()
                if thread_count > self.max_threads_per_process:
                    issues.append(f"主进程线程过多: {thread_count}")
                    
                # 检查文件描述符
                try:
                    open_files_count = len(main_process.open_files())
                    if open_files_count > self.max_open_files_per_process:
                        issues.append(f"主进程文件描述符过多: {open_files_count}")
                except (psutil.AccessDenied, psutil.NoSuchProcess):
                    pass
                    
            # 4. 检查是否有失控的Chromium进程
            long_running_chrome = self._find_long_running_chrome()
            if long_running_chrome:
                issues.append(f"发现长时间运行的Chrome进程: {len(long_running_chrome)}个")
            
            if issues:
                logger.warning(f"检测到系统问题: {'; '.join(issues)}")
                self._handle_emergency(issues)
            else:
                logger.debug("系统状态正常")
                
        except ImportError:
            logger.warning("psutil不可用，使用基础检查")
            self._basic_system_check()
        except Exception as e:
            logger.error(f"系统健康检查时出错: {e}")
    
    def _count_chrome_processes(self):
        """统计Chrome进程数量"""
        try:
            import psutil
            count = 0
            for proc in psutil.process_iter(['name']):
                try:
                    if 'chrome' in proc.info['name'].lower():
                        count += 1
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            return count
        except ImportError:
            # 回退方法
            try:
                result = subprocess.run(['pgrep', '-c', 'chrome'], capture_output=True, text=True)
                return int(result.stdout.strip()) if result.returncode == 0 else 0
            except:
                return 0
    
    def _find_main_process(self):
        """找到主程序进程"""
        try:
            import psutil
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    proc_info = proc.info
                    cmdline = ' '.join(proc_info.get('cmdline', []))
                    if 'main.py' in cmdline and 'python' in proc_info['name'].lower():
                        return proc
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            return None
        except ImportError:
            return None
    
    def _find_long_running_chrome(self):
        """找到长时间运行的Chrome进程"""
        try:
            import psutil
            long_running = []
            current_time = time.time()
            
            for proc in psutil.process_iter(['pid', 'name', 'create_time', 'cmdline']):
                try:
                    proc_info = proc.info
                    if 'chrome' in proc_info['name'].lower():
                        runtime_hours = (current_time - proc_info['create_time']) / 3600
                        if runtime_hours > 2:  # 运行超过2小时
                            long_running.append(proc_info['pid'])
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                    
            return long_running
        except ImportError:
            return []
    
    def _basic_system_check(self):
        """基础系统检查（不依赖psutil）"""
        try:
            # 检查内存
            with open('/proc/meminfo', 'r') as f:
                meminfo = f.read()
                total_mem = None
                available_mem = None
                
                for line in meminfo.split('\n'):
                    if 'MemTotal:' in line:
                        total_mem = int(line.split()[1])
                    elif 'MemAvailable:' in line:
                        available_mem = int(line.split()[1])
                        
                if total_mem and available_mem:
                    used_percent = (total_mem - available_mem) / total_mem * 100
                    if used_percent > self.max_memory_percent:
                        logger.warning(f"内存使用率过高: {used_percent:.1f}%")
                        self._handle_emergency([f"内存使用率过高: {used_percent:.1f}%"])
                        
        except Exception as e:
            logger.debug(f"基础系统检查失败: {e}")
    
    def _handle_emergency(self, issues):
        """处理紧急情况"""
        logger.error(f"紧急情况: {'; '.join(issues)}")
        
        # 检查重启频率限制
        now = datetime.now()
        self.restart_times = [t for t in self.restart_times if now - t < timedelta(hours=1)]
        
        if len(self.restart_times) >= self.max_restarts_per_hour:
            logger.error(f"一小时内重启次数已达上限({self.max_restarts_per_hour})，停止自动重启")
            return
            
        # 执行紧急清理
        self._emergency_cleanup()
        
        # 如果问题严重，重启主程序
        if self._is_critical_emergency(issues):
            logger.error("检测到严重问题，将重启主程序")
            self._restart_main_program()
    
    def _is_critical_emergency(self, issues):
        """判断是否为严重紧急情况需要重启"""
        critical_keywords = ['线程过多', '文件描述符过多', '内存使用率过高']
        return any(keyword in issue for issue in issues for keyword in critical_keywords)
    
    def _emergency_cleanup(self):
        """执行紧急清理"""
        logger.warning("执行紧急清理...")
        
        try:
            # 强制清理Chrome进程
            result = subprocess.run(['pkill', '-f', 'chrome'], capture_output=True)
            logger.info(f"Chrome进程清理完成，返回码: {result.returncode}")
            
            # 清理临时文件
            subprocess.run(['find', '/tmp', '-name', '*playwright*', '-type', 'd', '-exec', 'rm', '-rf', '{}', '+'], 
                         capture_output=True)
            subprocess.run(['find', '/tmp', '-name', '*chromium*', '-type', 'd', '-exec', 'rm', '-rf', '{}', '+'], 
                         capture_output=True)
            
            logger.info("紧急清理完成")
            
        except Exception as e:
            logger.error(f"紧急清理时出错: {e}")
    
    def _restart_main_program(self):
        """重启主程序"""
        try:
            # 记录重启时间
            self.restart_times.append(datetime.now())
            
            # 查找并终止主程序
            result = subprocess.run(['pkill', '-f', 'main.py'], capture_output=True)
            logger.info(f"主程序终止，返回码: {result.returncode}")
            
            # 等待进程完全终止
            time.sleep(5)
            
            # 重新启动主程序
            subprocess.Popen(['python', 'main.py'], 
                           stdout=subprocess.DEVNULL, 
                           stderr=subprocess.DEVNULL,
                           preexec_fn=os.setsid)
            
            logger.info("主程序已重启")
            
        except Exception as e:
            logger.error(f"重启主程序时出错: {e}")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False

def handle_signal(signum, frame):
    """信号处理器"""
    logger.info(f"收到信号 {signum}，准备退出监控")
    monitor.stop_monitoring()

if __name__ == "__main__":
    # 注册信号处理器
    signal.signal(signal.SIGINT, handle_signal)
    signal.signal(signal.SIGTERM, handle_signal)
    
    # 创建并启动监控
    monitor = EmergencyMonitor()
    
    try:
        monitor.start_monitoring()
    except Exception as e:
        logger.error(f"监控程序异常退出: {e}", exc_info=True)
        sys.exit(1) 