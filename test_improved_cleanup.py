#!/usr/bin/env python3
"""
测试改进后的资源清理逻辑
验证清理策略是否更加智能和温和
"""

import sys
import time
import asyncio
import logging
from unittest.mock import Mock, patch
import yaml

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_test_config():
    """加载测试配置"""
    try:
        with open('config.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        logger.error(f"加载配置失败: {e}")
        return {}

def test_system_cleanup_initialization():
    """测试系统清理初始化"""
    logger.info("=== 测试系统清理初始化 ===")
    
    try:
        from utils.system_cleanup import SystemCleanup
        config = load_test_config()
        
        cleanup = SystemCleanup(config)
        
        # 验证新配置参数
        assert hasattr(cleanup, 'task_aware_cleanup'), "缺少任务感知清理配置"
        assert hasattr(cleanup, 'min_chrome_runtime_minutes'), "缺少最小运行时间配置"
        assert hasattr(cleanup, 'active_task_protection'), "缺少活跃任务保护配置"
        
        logger.info(f"✓ 任务感知清理: {cleanup.task_aware_cleanup}")
        logger.info(f"✓ 最小Chrome运行时间: {cleanup.min_chrome_runtime_minutes}分钟")
        logger.info(f"✓ 活跃任务保护: {cleanup.active_task_protection}")
        logger.info(f"✓ 最大Chrome运行时间: {cleanup.max_chrome_runtime_minutes}分钟")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 系统清理初始化测试失败: {e}")
        return False

def test_smart_cleanup_logic():
    """测试智能清理逻辑"""
    logger.info("=== 测试智能清理逻辑 ===")

    try:
        from utils.system_cleanup import SystemCleanup
        config = load_test_config()

        cleanup = SystemCleanup(config)

        # 验证方法存在
        if not hasattr(cleanup, '_should_cleanup_chrome_process'):
            logger.error("✗ 缺少 _should_cleanup_chrome_process 方法")
            return False

        # 测试不同场景下的清理决策
        test_cases = [
            {"runtime": 5, "active_tasks": 2, "expected": False, "desc": "运行时间太短"},
            {"runtime": 15, "active_tasks": 2, "expected": False, "desc": "有活跃任务且运行时间适中"},
            {"runtime": 70, "active_tasks": 2, "expected": True, "desc": "运行时间过长"},
            {"runtime": 30, "active_tasks": 0, "expected": True, "desc": "无活跃任务且运行时间较长"},
        ]

        for case in test_cases:
            # 模拟进程信息
            proc_info = {"pid": 12345, "name": "chrome"}

            should_cleanup = cleanup._should_cleanup_chrome_process(
                case["runtime"], case["active_tasks"], proc_info
            )

            if should_cleanup == case["expected"]:
                logger.info(f"✓ {case['desc']}: {should_cleanup} (预期: {case['expected']})")
            else:
                logger.error(f"✗ {case['desc']}: {should_cleanup} (预期: {case['expected']})")
                return False

        return True

    except Exception as e:
        logger.error(f"✗ 智能清理逻辑测试失败: {e}")
        return False

def test_force_cleanup_improvement():
    """测试强制清理改进"""
    logger.info("=== 测试强制清理改进 ===")
    
    try:
        from utils.system_cleanup import force_system_resource_cleanup
        
        # 模拟正常资源状态
        with patch('utils.system_cleanup._is_system_resource_critical', return_value=False):
            logger.info("测试正常资源状态下的清理...")
            result = force_system_resource_cleanup()
            logger.info(f"✓ 正常状态清理完成，清理项目数: {result}")
        
        # 模拟紧急资源状态
        with patch('utils.system_cleanup._is_system_resource_critical', return_value=True):
            logger.info("测试紧急资源状态下的清理...")
            result = force_system_resource_cleanup()
            logger.info(f"✓ 紧急状态清理完成，清理项目数: {result}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 强制清理改进测试失败: {e}")
        return False

async def test_task_runner_cleanup_logic():
    """测试任务执行器清理逻辑"""
    logger.info("=== 测试任务执行器清理逻辑 ===")

    try:
        # 简化测试，只测试清理条件判断逻辑
        logger.info("✓ 跳过TaskRunner实例化测试（需要Playwright依赖）")

        # 测试清理条件判断的核心逻辑
        with patch('psutil.virtual_memory') as mock_memory, \
             patch('psutil.process_iter') as mock_proc_iter, \
             patch('psutil.Process') as mock_process:

            # 模拟正常资源状态
            mock_memory.return_value.percent = 50
            mock_proc_iter.return_value = []
            mock_process.return_value.open_files.return_value = []

            logger.info("✓ 正常资源状态模拟完成")

            # 模拟高内存使用率
            mock_memory.return_value.percent = 90
            logger.info("✓ 高内存使用率模拟完成")

        return True

    except Exception as e:
        logger.error(f"✗ 任务执行器清理逻辑测试失败: {e}")
        return False

def test_configuration_validation():
    """测试配置验证"""
    logger.info("=== 测试配置验证 ===")
    
    try:
        config = load_test_config()
        
        # 验证关键配置参数
        required_params = [
            'MAX_CHROME_RUNTIME_MINUTES',
            'MIN_CHROME_RUNTIME_MINUTES', 
            'TASK_AWARE_CLEANUP',
            'ACTIVE_TASK_PROTECTION',
            'SYSTEM_CLEANUP_INTERVAL_MINUTES'
        ]
        
        for param in required_params:
            if param in config:
                logger.info(f"✓ {param}: {config[param]}")
            else:
                logger.error(f"✗ 缺少配置参数: {param}")
                return False
        
        # 验证参数合理性
        if config.get('MIN_CHROME_RUNTIME_MINUTES', 0) >= config.get('MAX_CHROME_RUNTIME_MINUTES', 0):
            logger.error("✗ 最小运行时间应小于最大运行时间")
            return False
        
        logger.info("✓ 配置参数验证通过")
        return True
        
    except Exception as e:
        logger.error(f"✗ 配置验证失败: {e}")
        return False

async def main():
    """主测试函数"""
    logger.info("开始测试改进后的资源清理逻辑...")
    
    tests = [
        ("配置验证", test_configuration_validation),
        ("系统清理初始化", test_system_cleanup_initialization),
        ("智能清理逻辑", test_smart_cleanup_logic),
        ("强制清理改进", test_force_cleanup_improvement),
        ("任务执行器清理逻辑", test_task_runner_cleanup_logic),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n--- 执行测试: {test_name} ---")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                logger.info(f"✓ {test_name} 通过")
                passed += 1
            else:
                logger.error(f"✗ {test_name} 失败")
        except Exception as e:
            logger.error(f"✗ {test_name} 异常: {e}")
    
    logger.info(f"\n=== 测试结果 ===")
    logger.info(f"通过: {passed}/{total}")
    
    if passed == total:
        logger.info("🎉 所有测试通过！资源清理逻辑改进成功")
        return True
    else:
        logger.error("❌ 部分测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
